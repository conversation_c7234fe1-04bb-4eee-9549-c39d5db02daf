import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'form_config.dart';
import 'form_keys.dart';


/// Universal form controller for managing form state, validation, and operations
class UniversalFormController with ChangeNotifier {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final List<FormFieldConfig> _fields;
  
  // State management with granular notifications
  final Map<String, dynamic> _formValues = {};
  final Map<String, ValueNotifier<dynamic>> _valueNotifiers = {};
  final Map<String, String> _validationErrors = {};
  final Map<String, ValueNotifier<String?>> _errorNotifiers = {};
  Map<String, dynamic>? _snapshot;

  // Options caching for dropdown fields
  final Map<String, List<dynamic>> _optionsCache = {};
  final Map<String, bool> _optionsLoading = {};
  final Map<String, String?> _optionsErrors = {};
  final Map<String, Future<List<dynamic>>> _inFlightRequests = {};

  // Text controllers for text fields
  final Map<String, TextEditingController> _textControllers = {};

  bool _isSaving = false;
  
  // Getters
  List<FormFieldConfig> get fields => _fields;
  Map<String, dynamic> get formValues => Map.unmodifiable(_formValues);
  Map<String, String> get validationErrors => Map.unmodifiable(_validationErrors);
  bool get isSaving => _isSaving;
  /// Dynamic isDirty calculation based on actual value comparison
  /// Returns true if current values differ from snapshot
  bool get isDirty {
    if (_snapshot == null) return _formValues.isNotEmpty;
    return !mapEquals(_formValues, _snapshot!);
  }
  bool get isValid => _validationErrors.isEmpty;

  /// Get ValueNotifier for a specific field's value (for granular listening)
  ValueNotifier<dynamic> getValueNotifier(String key) {
    return _valueNotifiers.putIfAbsent(key, () => ValueNotifier(_formValues[key]));
  }

  /// Get ValueNotifier for a specific field's error (for granular listening)
  ValueNotifier<String?> getErrorNotifier(String key) {
    return _errorNotifiers.putIfAbsent(key, () => ValueNotifier(_validationErrors[key]));
  }

  UniversalFormController({
    required List<FormFieldConfig> fields,
    Map<String, dynamic>? initialValues,
  }) : _fields = fields {
    _initializeForm(initialValues);
  }

  /// Initialize form with fields and initial values
  void _initializeForm(Map<String, dynamic>? initialValues) {
    for (final field in _fields) {
      final fieldKey = field.key.value;

      // Set initial values
      final initialValue = initialValues?[fieldKey] ?? field.initialValue;
      if (initialValue != null) {
        _formValues[fieldKey] = initialValue;
      }


    }
    
    // Create initial snapshot for cancel functionality
    _createSnapshot();
  }



  /// Set value for a specific field (using string key)
  void setValue(String key, dynamic value, {bool validate = true}) {
    _updateValue(key, value, validate: validate);
  }

  /// Set value for a specific field (using FormKeys enum)
  void setValueByKey(FormKeys key, dynamic value, {bool validate = true}) {
    _updateValue(key.value, value, validate: validate);
  }

  /// Internal method to update value with granular notifications
  /// Pure state management - no UI concerns
  void _updateValue(String key, dynamic value, {bool validate = true, bool notifyListeners = true}) {
    final oldValue = _formValues[key];
    _formValues[key] = value;

    if (oldValue != value) {
      // Update granular ValueNotifier for this field
      if (_valueNotifiers.containsKey(key)) {
        _valueNotifiers[key]!.value = value;
      }

      // Clear validation error for this field
      _validationErrors.remove(key);

      // Update granular error notifier
      if (_errorNotifiers.containsKey(key)) {
        _errorNotifiers[key]!.value = null;
      }

      // Validate if requested
      if (validate) {
        _validateField(key);
      }

      // Handle field dependencies
      _handleFieldDependencies(key, value);

      // Only notify global listeners if no granular listeners exist
      // This provides backward compatibility while optimizing performance
      if (notifyListeners && (_valueNotifiers.isEmpty && _errorNotifiers.isEmpty)) {
        this.notifyListeners();
      }
    }
  }

  /// Get value for a specific field (using string key)
  dynamic getValue(String key) {
    return _formValues[key];
  }

  /// Get value for a specific field (using FormKeys enum)
  dynamic getValueByKey(FormKeys key) {
    return _formValues[key.value];
  }

  /// Get all form values
  Map<String, dynamic> getAllValues() {
    return Map.unmodifiable(_formValues);
  }

  /// Load options for a dropdown field with caching and de-duplication
  /// Uses Completer pattern to avoid polling and handle concurrent requests efficiently
  Future<List<dynamic>> loadOptionsForField(String fieldKey, Future<List<dynamic>> Function() loader) async {
    // Check cache first
    if (_optionsCache.containsKey(fieldKey)) {
      return _optionsCache[fieldKey]!;
    }

    // Check if already loading - return existing future
    if (_inFlightRequests.containsKey(fieldKey)) {
      return await _inFlightRequests[fieldKey]!;
    }

    // Create completer for this request
    final completer = Completer<List<dynamic>>();
    _inFlightRequests[fieldKey] = completer.future;

    // Set loading state
    _optionsLoading[fieldKey] = true;
    _optionsErrors[fieldKey] = null;

    // Notify listeners immediately
    notifyListeners();

    try {
      final options = await loader();
      _optionsCache[fieldKey] = options;
      _optionsLoading[fieldKey] = false;

      // Complete the future
      completer.complete(options);

      // Notify listeners immediately
      notifyListeners();

      return options;
    } catch (e) {
      _optionsLoading[fieldKey] = false;
      _optionsErrors[fieldKey] = e.toString();

      // Complete with error
      completer.completeError(e);

      // Notify listeners immediately
      notifyListeners();

      rethrow;
    } finally {
      // Always remove from in-flight requests
      _inFlightRequests.remove(fieldKey);
    }
  }

  /// Get cached options for a field
  List<dynamic>? getOptions(String fieldKey) {
    return _optionsCache[fieldKey];
  }

  /// Check if options are loading for a field
  bool isLoadingOptions(String fieldKey) {
    return _optionsLoading[fieldKey] ?? false;
  }

  /// Get options loading error for a field
  String? getOptionsError(String fieldKey) {
    return _optionsErrors[fieldKey];
  }

  /// Clear options cache for a field
  void clearOptionsCache(String fieldKey) {
    _optionsCache.remove(fieldKey);
    _optionsLoading.remove(fieldKey);
    _optionsErrors.remove(fieldKey);
    notifyListeners();
  }

  /// Clear all options cache
  void clearAllOptionsCache() {
    _optionsCache.clear();
    _optionsLoading.clear();
    _optionsErrors.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    // Dispose all ValueNotifiers to prevent memory leaks
    for (final notifier in _valueNotifiers.values) {
      notifier.dispose();
    }
    for (final notifier in _errorNotifiers.values) {
      notifier.dispose();
    }

    // Dispose text controllers
    for (final controller in _textControllers.values) {
      controller.dispose();
    }

    _valueNotifiers.clear();
    _errorNotifiers.clear();
    _textControllers.clear();

    super.dispose();
  }

  /// Validate a specific field with granular error notifications
  void _validateField(String key) {
    final field = _fields.firstWhere((f) => f.key.value == key, orElse: () => throw ArgumentError('Field $key not found'));
    final value = _formValues[key];

    String? errorMessage;
    for (final rule in field.validationRules) {
      final error = _validateRule(rule, value, key);
      if (error != null) {
        errorMessage = error;
        _validationErrors[key] = error;
        break;
      }
    }

    // Remove error if validation passes
    if (errorMessage == null) {
      _validationErrors.remove(key);
    }

    // Update granular error notifier
    if (_errorNotifiers.containsKey(key)) {
      _errorNotifiers[key]!.value = errorMessage;
    }
  }

  /// Validate a single rule using the rule's own validation logic
  /// This delegates to the ValidationRule class for better separation of concerns
  String? _validateRule(ValidationRule rule, dynamic value, String fieldKey) {
    return rule.validate(value, _formValues);
  }

  /// Validate entire form
  bool validate() {
    _validationErrors.clear();
    
    for (final field in _fields) {
      _validateField(field.key.value);
    }
    
    notifyListeners();
    return _validationErrors.isEmpty;
  }

  /// Handle field dependencies with intelligent data management
  /// Performs actions like clearing dependent field values and refreshing options
  void _handleFieldDependencies(String changedFieldKey, dynamic newValue) {
    for (final field in _fields) {
      if (field.dependencies != null) {
        for (final dependency in field.dependencies!) {
          if (dependency.parentFieldKey.value == changedFieldKey) {
            final dependentFieldKey = field.key.value;

            // Check if the dependent field should now be hidden
            final shouldBeVisible = _evaluateDependencyCondition(dependency, newValue);

            if (!shouldBeVisible) {
              // Clear the dependent field's value when it becomes hidden
              // This ensures data consistency
              if (_formValues.containsKey(dependentFieldKey)) {
                _updateValue(dependentFieldKey, null, validate: false, notifyListeners: false);
              }
            }

            // If this is a dropdown field with dynamic options, clear its cache
            // This will trigger a fresh load with the new parent value
            if (field.type == FormFieldType.dropdown && field.optionsLoader != null) {
              _optionsCache.remove(dependentFieldKey);
              _optionsLoading.remove(dependentFieldKey);
              _optionsErrors.remove(dependentFieldKey);
            }
          }
        }
      }
    }
  }

  /// Evaluate if a dependency condition is satisfied
  bool _evaluateDependencyCondition(FieldDependency dependency, dynamic parentValue) {
    // Use custom evaluate function if provided
    if (dependency.evaluate != null) {
      return dependency.evaluate!(_formValues);
    }

    // Use simple value matching if parentValue is specified
    if (dependency.parentValue != null) {
      return parentValue == dependency.parentValue;
    }

    // Default: dependency is satisfied if parent has any meaningful value
    return parentValue != null &&
           (parentValue is! String || parentValue.isNotEmpty) &&
           (parentValue is! bool || parentValue);
  }

  /// Create snapshot for cancel functionality
  void _createSnapshot() {
    _snapshot = Map.from(_formValues);
  }

  /// Revert to snapshot (cancel changes)
  /// Pure state management - UI widgets will listen to ValueNotifiers for updates
  void revertToSnapshot() {
    if (_snapshot != null) {
      _formValues.clear();
      _formValues.addAll(_snapshot!);
      _validationErrors.clear();

      // Update all ValueNotifiers to notify UI widgets
      for (final entry in _formValues.entries) {
        if (_valueNotifiers.containsKey(entry.key)) {
          _valueNotifiers[entry.key]!.value = entry.value;
        }
      }

      // Clear all error notifiers
      for (final notifier in _errorNotifiers.values) {
        notifier.value = null;
      }

      notifyListeners();
    }
  }

  /// Set saving state
  void setSaving(bool saving) {
    _isSaving = saving;
    notifyListeners();
  }

  /// Reset form to initial state
  void reset({Map<String, dynamic>? newInitialValues}) {
    _formValues.clear();
    _validationErrors.clear();
    _snapshot = null; // Reset snapshot to make isDirty false
    
    // Reinitialize with new values if provided
    if (newInitialValues != null) {
      _initializeForm(newInitialValues);
    } else {
      _initializeForm(null);
    }
    
    notifyListeners();
  }

}
