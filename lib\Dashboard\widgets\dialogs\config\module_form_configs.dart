import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../form_config.dart';
import '../form_keys.dart';
import '../form_data_service.dart';
import '../form_enums.dart';


/// Centralized configuration service for all module forms
/// Defines the structure and behavior of every form in the application
///
/// This class uses dependency injection via GetIt to access FormDataService
/// eliminating the need for constructor injection or factory patterns.
class ModuleFormConfigs {
  /// Get the FormDataService from dependency injection container
  static FormDataService get _dataService => GetIt.instance<FormDataService>();

  /// Create a new ModuleFormConfigs instance
  const ModuleFormConfigs();

  /// Weight form configuration
  List<FormFieldConfig> forWeight() {
    return [
      // Row 1: Cattle Selection (full width)
      FormFieldConfig<String>(
        key: FormKeys.cattle,
        label: 'Select Cattle',
        type: FormFieldType.cattleSelector,
        columnSpan: 1.0,
        icon: Icons.pets,
        validationRules: const [RequiredRule<String>()],
        optionsLoader: () async {
          final result = await _dataService.loadCattle();
          return result.unwrapOr([]);
        },
      ),

      // Row 2: Weight and Unit (half width each)
      const FormFieldConfig<double>(
        key: FormKeys.weight,
        label: 'Weight',
        type: FormFieldType.number,
        columnSpan: 0.5,
        icon: Icons.monitor_weight,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        decimalPlaces: 2,
        validationRules: const [
          RequiredRule<double>(),
        ],
      ),

      FormFieldConfig<String>(
        key: FormKeys.weightUnit,
        label: 'Unit',
        type: FormFieldType.dropdown,
        columnSpan: 0.5,
        icon: Icons.straighten,
        initialValue: 'kg',
        validationRules: const [RequiredRule<String>()],
        options: WeightUnit.dropdownOptions,
      ),

      // Row 3: Date and Method (half width each)
      FormFieldConfig<DateTime>(
        key: FormKeys.measurementDate,
        label: 'Measurement Date',
        type: FormFieldType.date,
        columnSpan: 0.5,
        icon: Icons.calendar_today,
        validationRules: const [RequiredRule<DateTime>()],
      ),

      FormFieldConfig<String>(
        key: FormKeys.measurementMethod,
        label: 'Method',
        type: FormFieldType.dropdown,
        columnSpan: 0.5,
        icon: Icons.scale,
        initialValue: 'scale',
        validationRules: const [RequiredRule<String>()],
        options: MeasurementMethod.dropdownOptions,
      ),

      // Row 4: Location and Quality (half width each)
      FormFieldConfig(
        key: FormKeys.measurementLocation,
        label: 'Location',
        type: FormFieldType.dropdown,
        columnSpan: 0.5,
        icon: Icons.location_on,
        initialValue: 'barn',
        options: CommonDropdownOptions.locationOptions,
      ),

      FormFieldConfig(
        key: FormKeys.measurementQuality,
        label: 'Quality',
        type: FormFieldType.dropdown,
        columnSpan: 0.5,
        icon: Icons.star,
        initialValue: 'good',
        options: CommonDropdownOptions.qualityOptions,
      ),

      // Row 5: Health Status and Feeding Status (half width each)
      FormFieldConfig(
        key: FormKeys.healthStatus,
        label: 'Health Status',
        type: FormFieldType.dropdown,
        columnSpan: 0.5,
        icon: Icons.health_and_safety,
        initialValue: 'healthy',
        options: CommonDropdownOptions.healthStatusOptions,
      ),

      FormFieldConfig(
        key: FormKeys.feedingStatus,
        label: 'Feeding Status',
        type: FormFieldType.dropdown,
        columnSpan: 0.5,
        icon: Icons.grass,
        initialValue: 'normal',
        options: CommonDropdownOptions.feedingStatusOptions,
      ),

      // Row 6: Body Condition Score and Pregnancy Status (half width each)
      FormFieldConfig<num>(
        key: FormKeys.bodyConditionScore,
        label: 'Body Condition Score',
        type: FormFieldType.number,
        columnSpan: 0.5,
        icon: Icons.fitness_center,
        hint: '1.0 - 5.0',
        validationRules: const <ValidationRule<num>>[
          RequiredRule<num>(),
          MinValueRule(1.0),
          MaxValueRule(5.0),
        ],
      ),

      FormFieldConfig<bool>(
        key: FormKeys.isPregnant,
        label: 'Pregnant',
        type: FormFieldType.checkbox,
        columnSpan: 0.5,
        icon: Icons.pregnant_woman,
      ),

      // Row 7: Pregnancy Stage (conditional, full width)
      FormFieldConfig<num>(
        key: FormKeys.pregnancyStage,
        label: 'Pregnancy Stage (months)',
        type: FormFieldType.number,
        columnSpan: 1.0,
        icon: Icons.timeline,
        validationRules: const <ValidationRule<num>>[
          MinValueRule(1),
          MaxValueRule(9),
        ],
        dependencies: const [
          FieldDependency(
            parentFieldKey: FormKeys.isPregnant,
            parentValue: true,
          ),
        ],
      ),

      // Row 8: Season and Feed Quality (half width each)
      FormFieldConfig(
        key: FormKeys.season,
        label: 'Season',
        type: FormFieldType.dropdown,
        columnSpan: 0.5,
        icon: Icons.wb_sunny,
        initialValue: 'spring',
        options: CommonDropdownOptions.seasonOptions,
      ),

      FormFieldConfig(
        key: FormKeys.feedQuality,
        label: 'Feed Quality',
        type: FormFieldType.dropdown,
        columnSpan: 0.5,
        icon: Icons.eco,
        initialValue: 'good',
        options: CommonDropdownOptions.qualityOptions,
      ),

      // Row 9: Estimate and Confidence (half width each)
      FormFieldConfig<bool>(
        key: FormKeys.isEstimate,
        label: 'Is Estimate',
        type: FormFieldType.checkbox,
        columnSpan: 0.5,
        icon: Icons.help_outline,
      ),

      FormFieldConfig<num>(
        key: FormKeys.confidenceLevel,
        label: 'Confidence Level',
        type: FormFieldType.slider,
        columnSpan: 0.5,
        icon: Icons.trending_up,
        initialValue: 1.0,
        validationRules: const <ValidationRule<num>>[
          MinValueRule(0.0),
          MaxValueRule(1.0),
        ],
      ),
    ];
  }



  /// Health record form configuration
  List<FormFieldConfig> forHealthRecord() {
    return [
      // Cattle selection
      FormFieldConfig<String>(
        key: FormKeys.cattle,
        label: 'Select Cattle',
        type: FormFieldType.cattleSelector,
        columnSpan: 1.0,
        icon: Icons.pets,
        validationRules: const [RequiredRule<String>()],
        optionsLoader: () async {
          final result = await _dataService.loadCattle();
          return result.unwrapOr([]);
        },
      ),

      // Date and condition
      FormFieldConfig<DateTime>(
        key: FormKeys.date,
        label: 'Date',
        type: FormFieldType.date,
        columnSpan: 0.5,
        icon: Icons.calendar_today,
        validationRules: const [RequiredRule<DateTime>()],
      ),

      FormFieldConfig<String>(
        key: FormKeys.condition,
        label: 'Condition/Diagnosis',
        type: FormFieldType.text,
        columnSpan: 0.5,
        icon: Icons.medical_services,
        validationRules: const [RequiredRule<String>()],
      ),

      // Treatment and veterinarian
      FormFieldConfig<String>(
        key: FormKeys.treatment,
        label: 'Treatment',
        type: FormFieldType.multiline,
        columnSpan: 1.0,
        icon: Icons.healing,
        maxLines: 3,
        validationRules: const [RequiredRule<String>()],
      ),

      FormFieldConfig<String>(
        key: FormKeys.veterinarian,
        label: 'Veterinarian',
        type: FormFieldType.dropdown,
        columnSpan: 0.5,
        icon: Icons.person,
        optionsLoader: () async {
          final result = await _dataService.loadVeterinarians();
          return result.unwrapOr([]);
        },
      ),

      FormFieldConfig<num>(
        key: FormKeys.cost,
        label: 'Cost',
        type: FormFieldType.currency,
        columnSpan: 0.5,
        icon: Icons.attach_money,
        validationRules: const <ValidationRule<num>>[PositiveNumberRule()],
      ),

      // Notes
      FormFieldConfig<String>(
        key: FormKeys.notes,
        label: 'Notes',
        type: FormFieldType.multiline,
        columnSpan: 1.0,
        icon: Icons.note,
        maxLines: 4,
      ),
    ];
  }

  /// Milk record form configuration
  List<FormFieldConfig> forMilkRecord() {
    return [
      FormFieldConfig<String>(
        key: FormKeys.cattle,
        label: 'Select Cattle',
        type: FormFieldType.dropdown,
        icon: Icons.pets,
        validationRules: const [RequiredRule<String>()],
        optionsLoader: () async {
          final result = await _dataService.loadCattle();
          return result.unwrapOr([]);
        },
      ),
      FormFieldConfig<DateTime>(
        key: FormKeys.date,
        label: 'Date',
        type: FormFieldType.date,
        validationRules: const [RequiredRule<DateTime>()],
        icon: Icons.calendar_today,
      ),
      FormFieldConfig<double>(
        key: FormKeys.morningAmount,
        label: 'Morning (L)',
        type: FormFieldType.number,
        validationRules: const [RequiredRule<double>()],
        columnSpan: 0.5,
        icon: Icons.wb_sunny,
      ),
      FormFieldConfig<double>(
        key: FormKeys.eveningAmount,
        label: 'Evening (L)',
        type: FormFieldType.number,
        validationRules: const [RequiredRule<double>()],
        columnSpan: 0.5,
        icon: Icons.nights_stay,
      ),
      FormFieldConfig<String>(
        key: FormKeys.quality,
        label: 'Quality',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        options: MilkQuality.dropdownOptions,
        icon: Icons.star,
      ),
      FormFieldConfig<String>(
        key: FormKeys.notes,
        label: 'Notes',
        type: FormFieldType.multiline,
        maxLines: 2,
        icon: Icons.notes,
      ),
    ];
  }

  /// Breeding record form configuration
  List<FormFieldConfig> forBreedingRecord() {
    return [
      FormFieldConfig<String>(
        key: FormKeys.cattleId,
        label: 'Female Cattle',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        optionsLoader: () async {
          final result = await _dataService.loadCattle(const CattleFilterParams(gender: Gender.female));
          return result.unwrapOr([]);
        },
        icon: Icons.pets,
      ),
      FormFieldConfig<String>(
        key: FormKeys.bullId,
        label: 'Bull/Sire',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        optionsLoader: () async {
          final result = await _dataService.loadCattle();
          return result.unwrapOr([]);
        },
        icon: Icons.male,
      ),
      FormFieldConfig<DateTime>(
        key: FormKeys.date,
        label: 'Breeding Date',
        type: FormFieldType.date,
        validationRules: const [RequiredRule<DateTime>()],
        icon: Icons.calendar_today,
      ),
      FormFieldConfig<String>(
        key: FormKeys.method,
        label: 'Method',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        options: BreedingMethod.dropdownOptions,
        icon: Icons.science,
      ),
      FormFieldConfig<String>(
        key: FormKeys.status,
        label: 'Status',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        options: BreedingStatus.dropdownOptions,
        icon: Icons.info,
      ),
      FormFieldConfig<DateTime>(
        key: FormKeys.expectedDate,
        label: 'Expected Delivery Date',
        type: FormFieldType.date,
        icon: Icons.event,
      ),
      FormFieldConfig<String>(
        key: FormKeys.notes,
        label: 'Notes',
        type: FormFieldType.multiline,
        maxLines: 3,
        icon: Icons.notes,
      ),
    ];
  }

  /// Transaction form configuration
  List<FormFieldConfig> forTransaction() {
    return [
      FormFieldConfig<String>(
        key: FormKeys.type,
        label: 'Type',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        options: TransactionCategoryType.dropdownOptions,
        icon: Icons.swap_horiz,
      ),
      FormFieldConfig<String>(
        key: FormKeys.categoryId,
        label: 'Category',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        optionsLoader: () async {
          final result = await _dataService.loadTransactionCategories(TransactionCategoryType.expense);
          return result.unwrapOr([]);
        },
        icon: Icons.category,
      ),
      FormFieldConfig<double>(
        key: FormKeys.amount,
        label: 'Amount',
        type: FormFieldType.number,
        validationRules: const [RequiredRule<double>()],
        icon: Icons.attach_money,
      ),
      FormFieldConfig<DateTime>(
        key: FormKeys.date,
        label: 'Date',
        type: FormFieldType.date,
        validationRules: const [RequiredRule<DateTime>()],
        icon: Icons.calendar_today,
      ),
      FormFieldConfig<String>(
        key: FormKeys.description,
        label: 'Description',
        type: FormFieldType.text,
        validationRules: const [RequiredRule<String>()],
        icon: Icons.description,
      ),
      FormFieldConfig<String>(
        key: FormKeys.notes,
        label: 'Notes',
        type: FormFieldType.multiline,
        maxLines: 3,
        icon: Icons.notes,
      ),
    ];
  }

  /// Event form configuration
  List<FormFieldConfig> forEvent() {
    return [
      FormFieldConfig<String>(
        key: FormKeys.title,
        label: 'Event Title',
        type: FormFieldType.text,
        validationRules: const [RequiredRule<String>()],
        icon: Icons.event,
      ),
      FormFieldConfig<String>(
        key: FormKeys.type,
        label: 'Event Type',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        options: EventType.dropdownOptions,
        icon: Icons.category,
      ),
      FormFieldConfig<DateTime>(
        key: FormKeys.date,
        label: 'Event Date',
        type: FormFieldType.date,
        validationRules: const [RequiredRule<DateTime>()],
        icon: Icons.calendar_today,
      ),
      FormFieldConfig<String>(
        key: FormKeys.priority,
        label: 'Priority',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        options: Priority.dropdownOptions,
        icon: Icons.priority_high,
      ),
      FormFieldConfig<String>(
        key: FormKeys.description,
        label: 'Description',
        type: FormFieldType.multiline,
        maxLines: 3,
        icon: Icons.description,
      ),
      FormFieldConfig<String>(
        key: FormKeys.notes,
        label: 'Notes',
        type: FormFieldType.multiline,
        maxLines: 2,
        icon: Icons.notes,
      ),
    ];
  }

  /// Milk sale entry form configuration
  List<FormFieldConfig> forMilkSaleEntry(double availableMilk) {
    return [
      FormFieldConfig<double>(
        key: FormKeys.quantity,
        label: 'Quantity (L)',
        type: FormFieldType.number,
        validationRules: [
          const RequiredRule<double>(),
          const MinValueRule(0.1) as ValidationRule<double>,
          MaxValueRule(availableMilk) as ValidationRule<double>,
        ],
        columnSpan: 0.7,
        icon: Icons.water_drop,
      ),
      FormFieldConfig<String>(
        key: FormKeys.unit,
        label: 'Unit',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        options: Unit.dropdownOptions,
        columnSpan: 0.3,
        icon: Icons.straighten,
      ),
      FormFieldConfig<double>(
        key: FormKeys.price,
        label: 'Price per Unit',
        type: FormFieldType.number,
        validationRules: const [RequiredRule<double>()],
        icon: Icons.attach_money,
      ),
      FormFieldConfig<String>(
        key: FormKeys.buyer,
        label: 'Buyer Name',
        type: FormFieldType.text,
        validationRules: const [RequiredRule<String>()],
        icon: Icons.person,
      ),
      FormFieldConfig<String>(
        key: FormKeys.paymentMethod,
        label: 'Payment Method',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        options: PaymentMethod.dropdownOptions,
        columnSpan: 0.5,
        icon: Icons.payment,
      ),
      FormFieldConfig<String>(
        key: FormKeys.paymentStatus,
        label: 'Payment Status',
        type: FormFieldType.dropdown,
        validationRules: const [RequiredRule<String>()],
        options: PaymentStatus.dropdownOptions,
        columnSpan: 0.5,
        icon: Icons.check_circle,
      ),
      FormFieldConfig<String>(
        key: FormKeys.notes,
        label: 'Notes (Optional)',
        type: FormFieldType.multiline,
        maxLines: 3,
        icon: Icons.note,
      ),
    ];
  }

  /// Cattle form configuration with complex business logic
  List<FormFieldConfig> forCattle() {
    return [
      // Animal Type - First field as shown in screenshot
      FormFieldConfig<String>(
        key: FormKeys.animalType,
        label: 'Animal Type',
        type: FormFieldType.dropdown,
        columnSpan: 1.0,
        icon: Icons.pets,
        validationRules: const [RequiredRule<String>()],
        optionsLoader: () async {
          final result = await _dataService.loadAnimalTypes();
          return result.unwrapOr([]);
        },
      ),

      // Breed (dependent on animal type) - Second field
      FormFieldConfig<String>(
        key: FormKeys.breed,
        label: 'Breed',
        type: FormFieldType.dropdown,
        columnSpan: 1.0,
        icon: Icons.category,
        validationRules: const [RequiredRule<String>()],
        dependencies: [
          FieldDependency(
            parentFieldKey: FormKeys.animalType,
            evaluate: (formValues) => formValues[FormKeys.animalType.value] != null,
          ),
        ],
        optionsLoader: () async {
          final result = await _dataService.loadAllBreeds();
          return result.unwrapOr([]);
        },
      ),

      // Name field - Third field
      FormFieldConfig<String>(
        key: FormKeys.name,
        label: 'Name',
        type: FormFieldType.text,
        columnSpan: 1.0,
        icon: Icons.label,
        validationRules: const [RequiredRule<String>()],
      ),

      // Tag ID with auto-generation toggle - Fourth field
      FormFieldConfig<String>(
        key: FormKeys.tagId,
        label: 'Tag ID',
        type: FormFieldType.text,
        columnSpan: 0.7,
        icon: Icons.tag,
        validationRules: const [RequiredRule<String>()],
        hint: 'Format: Animal type prefix + number (e.g., C1, G2)',
      ),

      // Auto-generate toggle
      FormFieldConfig<bool>(
        key: FormKeys.autoGenerateTagId,
        label: 'Auto',
        type: FormFieldType.custom,
        columnSpan: 0.3,
        icon: Icons.auto_fix_high,
        initialValue: true,
        customProperties: {
          'widget': 'toggle_switch',
        },
      ),

      // Gender - Fifth field
      FormFieldConfig<String>(
        key: FormKeys.gender,
        label: 'Gender',
        type: FormFieldType.dropdown,
        columnSpan: 1.0,
        icon: Icons.person,
        validationRules: const [RequiredRule<String>()],
        options: Gender.dropdownOptions,
      ),

      // Source - Sixth field
      FormFieldConfig<String>(
        key: FormKeys.source,
        label: 'Source',
        type: FormFieldType.dropdown,
        columnSpan: 1.0,
        icon: Icons.source,
        validationRules: const [RequiredRule<String>()],
        options: CattleSource.dropdownOptions,
      ),

      // Date of Birth (conditional on source)
      FormFieldConfig<DateTime>(
        key: FormKeys.dateOfBirth,
        label: 'Date of Birth',
        type: FormFieldType.date,
        columnSpan: 1.0,
        icon: Icons.cake,
        dependencies: [
          FieldDependency.onValue(FormKeys.source, CattleSource.bornAtFarm.value),
        ],
        validationRules: const [RequiredRule<DateTime>()],
      ),

      // Mother Tag ID (conditional on source)
      FormFieldConfig<String>(
        key: FormKeys.motherTagId,
        label: 'Mother Tag ID',
        type: FormFieldType.dropdown,
        columnSpan: 1.0,
        icon: Icons.family_restroom,
        dependencies: [
          FieldDependency.onValue(FormKeys.source, CattleSource.bornAtFarm.value),
        ],
        validationRules: const [RequiredRule<String>()],
        optionsLoader: () async {
          final result = await _dataService.loadFemaleCattle();
          return result.unwrapOr([]);
        },
      ),

      // Purchase Date (conditional on source)
      FormFieldConfig<DateTime>(
        key: FormKeys.purchaseDate,
        label: 'Purchase Date',
        type: FormFieldType.date,
        columnSpan: 1.0,
        icon: Icons.shopping_cart,
        dependencies: [
          FieldDependency.onValue(FormKeys.source, CattleSource.purchased.value),
        ],
        validationRules: const [RequiredRule<DateTime>()],
      ),

      // Purchase Price (conditional on source)
      FormFieldConfig<double>(
        key: FormKeys.purchasePrice,
        label: 'Purchase Price',
        type: FormFieldType.number,
        columnSpan: 1.0,
        icon: Icons.attach_money,
        dependencies: [
          FieldDependency.onValue(FormKeys.source, CattleSource.purchased.value),
        ],
        validationRules: const [RequiredRule<double>()],
      ),

      // Weight (optional) - Full width for consistency
      FormFieldConfig<double>(
        key: FormKeys.weight,
        label: 'Weight (kg)',
        type: FormFieldType.number,
        columnSpan: 1.0,
        icon: Icons.monitor_weight,
        validationRules: const [],
      ),

      // Color (optional) - Full width for consistency
      FormFieldConfig<String>(
        key: FormKeys.color,
        label: 'Color',
        type: FormFieldType.text,
        columnSpan: 1.0,
        icon: Icons.color_lens,
      ),

      // Notes (optional)
      FormFieldConfig<String>(
        key: FormKeys.notes,
        label: 'Notes',
        type: FormFieldType.multiline,
        columnSpan: 1.0,
        icon: Icons.note,
        maxLines: 3,
      ),
    ];
  }

  /// Example of how to use the new dependency injection architecture:
  ///
  /// ```dart
  /// // In your page/widget:
  /// final formConfigs = ModuleFormConfigs(); // Simple instantiation
  ///
  /// final config = FormConfig(
  ///   title: 'Weight Record',
  ///   fields: formConfigs.forWeight(), // Service automatically injected
  /// );
  ///
  /// final result = await UniversalFormDialog.show(
  ///   context: context,
  ///   config: config,
  ///   onSave: (values) async {
  ///     // Your save logic here
  ///     return true;
  ///   },
  /// );
  /// ```
  ///
  /// This dependency injection architecture provides:
  /// - Type safety with FormKeys enum (no more string typos)
  /// - Decoupled data loading (testable and modular)
  /// - Proper dependency injection (no singletons or factories)
  /// - Automatic service resolution via GetIt
  /// - Reusable DropdownOption model
  /// - Cleaner separation of concerns
}
