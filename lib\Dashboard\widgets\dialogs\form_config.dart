import 'package:flutter/material.dart';
import 'form_keys.dart';
import 'dropdown_option.dart';



/// Enum defining all supported form field types
enum FormFieldType {
  text,
  number,
  dropdown,
  date,
  multiSelect,
  colorPicker,
  location,
  cattleSelector,
  composite,
  custom,
  multiline,
  checkbox,
  slider,
  currency,
  percentage,
  phone,
  email,
  url,
  time,
  dateTime,
  autoComplete,
}

/// Validation rule types
enum ValidationRuleType {
  required,
  minLength,
  maxLength,
  minValue,
  maxValue,
  email,
  phone,
  url,
  pattern,
  custom,
  tagIdFormat,
  uniqueTagId,
  dateRange,
  positiveNumber,
  percentage,
}

/// Base class for type-safe validation rules
///
/// This sealed class hierarchy provides maximum type safety by ensuring
/// that validation rules can only be applied to appropriate field types.
abstract class ValidationRule<T> {
  final ValidationRuleType type;
  final String message;
  final bool Function(T? value, Map<String, dynamic> formValues)? customValidator;

  const ValidationRule({
    required this.type,
    required this.message,
    this.customValidator,
  });

  /// Validate the value using this rule's logic
  /// This method contains the actual validation implementation
  String? validate(dynamic value, Map<String, dynamic> formValues) {
    // Custom validator takes precedence
    if (customValidator != null) {
      try {
        final typedValue = value as T?;
        if (!customValidator!(typedValue, formValues)) {
          return message;
        }
      } catch (e) {
        // Type casting failed or custom validator threw
        return message;
      }
      return null;
    }

    // Use type-specific validation logic
    return validateTyped(value, formValues);
  }

  /// Type-specific validation logic - implemented by subclasses
  /// Default implementation handles common validation types
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    switch (type) {
      case ValidationRuleType.required:
        if (value == null || (value is String && value.trim().isEmpty)) {
          return message;
        }
        break;

      case ValidationRuleType.email:
        if (value is String && value.isNotEmpty) {
          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
            return message;
          }
        }
        break;

      case ValidationRuleType.phone:
        if (value is String && value.isNotEmpty) {
          if (!RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(value)) {
            return message;
          }
        }
        break;

      case ValidationRuleType.positiveNumber:
        if (value is num && value <= 0) {
          return message;
        }
        break;

      default:
        // For other types, delegate to subclass implementation
        break;
    }

    return null; // Validation passed
  }

  /// Common validation rules - use concrete classes instead
  @Deprecated('Use RequiredRule<T>() instead')
  static const RequiredRule required = RequiredRule();

  @Deprecated('Use MinLengthRule(length) instead')
  static MinLengthRule minLength(int length) => MinLengthRule(length);

  @Deprecated('Use MaxLengthRule(length) instead')
  static MaxLengthRule maxLength(int length) => MaxLengthRule(length);

  @Deprecated('Use MinValueRule(value) instead')
  static MinValueRule minValue(double value) => MinValueRule(value);

  @Deprecated('Use MaxValueRule(value) instead')
  static MaxValueRule maxValue(double value) => MaxValueRule(value);

  @Deprecated('Use EmailRule() instead')
  static const EmailRule email = EmailRule();

  @Deprecated('Use PhoneRule() instead')
  static const PhoneRule phone = PhoneRule();

  @Deprecated('Use PositiveNumberRule() instead')
  static const PositiveNumberRule positiveNumber = PositiveNumberRule();

  @Deprecated('Use TagIdFormatRule() instead')
  static const TagIdFormatRule tagIdFormat = TagIdFormatRule();

  @Deprecated('Use UniqueTagIdRule() instead')
  static const UniqueTagIdRule uniqueTagId = UniqueTagIdRule();

  @Deprecated('Use DateRangeRule(start, end) instead')
  static DateRangeRule dateRange(DateTime? start, DateTime? end) => DateRangeRule(start, end);

  @Deprecated('Use PatternRule(pattern, message) instead')
  static PatternRule pattern(String pattern, String message) => PatternRule(pattern, message);

  @Deprecated('Use CustomRule<T>(validator, message) instead')
  static CustomRule<dynamic> custom(
    bool Function(dynamic value, Map<String, dynamic> formValues) validator,
    String message,
  ) => CustomRule<dynamic>(validator, message);
}

/// Enhanced type-safe validation rules for maximum compile-time safety
///
/// These sealed classes ensure validation rules can only be applied to appropriate field types.
/// For example, MinLengthRule can only be used with String fields, preventing configuration errors.

/// Type-safe validation rule for required fields (applies to any type)
class RequiredRule<T> extends ValidationRule<T> {
  const RequiredRule({String message = 'This field is required'})
      : super(
          type: ValidationRuleType.required,
          message: message,
        );
}

/// Type-safe validation rule for minimum string length (String fields only)
class MinLengthRule extends ValidationRule<String> {
  final int minLength;

  const MinLengthRule(this.minLength, {String? message})
      : super(
          type: ValidationRuleType.minLength,
          message: message ?? 'Must be at least $minLength characters',
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    if (value is String && value.length < minLength) {
      return message;
    }
    return null;
  }
}

/// Type-safe validation rule for maximum string length (String fields only)
class MaxLengthRule extends ValidationRule<String> {
  final int maxLength;

  const MaxLengthRule(this.maxLength, {String? message})
      : super(
          type: ValidationRuleType.maxLength,
          message: message ?? 'Must be no more than $maxLength characters',
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    if (value is String && value.length > maxLength) {
      return message;
    }
    return null;
  }
}

/// Type-safe validation rule for minimum numeric value (numeric fields only)
class MinValueRule extends ValidationRule<num> {
  final num minValue;

  const MinValueRule(this.minValue, {String? message})
      : super(
          type: ValidationRuleType.minValue,
          message: message ?? 'Must be at least $minValue',
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    if (value is num && value < minValue) {
      return message;
    }
    return null;
  }
}

/// Type-safe validation rule for maximum numeric value (numeric fields only)
class MaxValueRule extends ValidationRule<num> {
  final num maxValue;

  const MaxValueRule(this.maxValue, {String? message})
      : super(
          type: ValidationRuleType.maxValue,
          message: message ?? 'Must be no more than $maxValue',
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    if (value is num && value > maxValue) {
      return message;
    }
    return null;
  }
}

/// Type-safe validation rule for email format (String fields only)
class EmailRule extends ValidationRule<String> {
  const EmailRule({String message = 'Please enter a valid email address'})
      : super(
          type: ValidationRuleType.email,
          message: message,
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    if (value is String && value.isNotEmpty) {
      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
        return message;
      }
    }
    return null;
  }
}

/// Type-safe validation rule for phone format (String fields only)
class PhoneRule extends ValidationRule<String> {
  const PhoneRule({String message = 'Please enter a valid phone number'})
      : super(
          type: ValidationRuleType.phone,
          message: message,
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    if (value is String && value.isNotEmpty) {
      if (!RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(value)) {
        return message;
      }
    }
    return null;
  }
}

/// Type-safe validation rule for positive numbers (numeric fields only)
class PositiveNumberRule extends ValidationRule<num> {
  const PositiveNumberRule({String message = 'Must be a positive number'})
      : super(
          type: ValidationRuleType.positiveNumber,
          message: message,
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    if (value is num && value <= 0) {
      return message;
    }
    return null;
  }
}

/// Type-safe validation rule for tag ID format (String fields only)
class TagIdFormatRule extends ValidationRule<String> {
  const TagIdFormatRule({String message = 'Tag ID must start with animal type first letter followed by numbers'})
      : super(
          type: ValidationRuleType.tagIdFormat,
          message: message,
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    if (value is String && value.isNotEmpty) {
      if (!RegExp(r'^[A-Z][0-9]+$').hasMatch(value)) {
        return message;
      }
    }
    return null;
  }
}

/// Type-safe validation rule for unique tag ID (String fields only)
class UniqueTagIdRule extends ValidationRule<String> {
  const UniqueTagIdRule({String message = 'This tag ID is already in use by another animal'})
      : super(
          type: ValidationRuleType.uniqueTagId,
          message: message,
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    // This would need to be implemented with actual database checking
    // For now, just return null (no validation)
    return null;
  }
}

/// Type-safe validation rule for date range (DateTime fields only)
class DateRangeRule extends ValidationRule<DateTime> {
  final DateTime? startDate;
  final DateTime? endDate;

  const DateRangeRule(this.startDate, this.endDate, {String message = 'Date must be within the allowed range'})
      : super(
          type: ValidationRuleType.dateRange,
          message: message,
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    if (value is DateTime) {
      if (startDate != null && value.isBefore(startDate!)) {
        return message;
      }
      if (endDate != null && value.isAfter(endDate!)) {
        return message;
      }
    }
    return null;
  }
}

/// Type-safe validation rule for pattern matching (String fields only)
class PatternRule extends ValidationRule<String> {
  final String pattern;

  const PatternRule(this.pattern, String message)
      : super(
          type: ValidationRuleType.pattern,
          message: message,
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    if (value is String && value.isNotEmpty) {
      if (!RegExp(pattern).hasMatch(value)) {
        return message;
      }
    }
    return null;
  }
}

/// Type-safe validation rule for custom validation logic
class CustomRule<T> extends ValidationRule<T> {
  final bool Function(T? value, Map<String, dynamic> formValues) validator;

  const CustomRule(this.validator, String message)
      : super(
          type: ValidationRuleType.custom,
          message: message,
        );

  @override
  String? validateTyped(dynamic value, Map<String, dynamic> formValues) {
    try {
      final typedValue = value as T?;
      if (!validator(typedValue, formValues)) {
        return message;
      }
    } catch (e) {
      return message;
    }
    return null;
  }
}

/// Convenience class providing static factory methods for enhanced type-safe validation rules
/// This provides the new type-safe API while maintaining backward compatibility
class ValidationRules {
  /// Required field validation (works with any field type)
  static RequiredRule<T> required<T>() => RequiredRule<T>();

  /// Minimum string length validation
  static MinLengthRule minLength(int length) => MinLengthRule(length);

  /// Maximum string length validation
  static MaxLengthRule maxLength(int length) => MaxLengthRule(length);

  /// Minimum numeric value validation
  static MinValueRule minValue(num value) => MinValueRule(value);

  /// Maximum numeric value validation
  static MaxValueRule maxValue(num value) => MaxValueRule(value);
}

// DropdownOption moved to lib/models/ui/dropdown_option.dart for wider reuse

/// Configuration for dependent field relationships
///
/// This class provides powerful dependency logic for complex form interactions.
/// The evaluate function receives the entire form state for multi-field dependencies.
class FieldDependency {
  final FormKeys parentFieldKey;
  final dynamic parentValue; // For simple value-based dependencies
  final bool Function(Map<String, dynamic> formValues)? evaluate;

  const FieldDependency({
    required this.parentFieldKey,
    this.parentValue,
    this.evaluate,
  });

  /// Helper constructor for simple value-based dependencies
  /// Example: FieldDependency.onValue(FormKeys.animalType, 'Cow')
  static FieldDependency onValue(FormKeys parentKey, dynamic value) {
    return FieldDependency(
      parentFieldKey: parentKey,
      parentValue: value,
      evaluate: (formValues) => formValues[parentKey.value] == value,
    );
  }

  /// Helper constructor for complex multi-field dependencies
  /// Example: FieldDependency.onCondition((values) => values['fieldA'] == 'X' && values['fieldB'] > 10)
  static FieldDependency onCondition(
    FormKeys parentKey,
    bool Function(Map<String, dynamic> formValues) condition,
  ) {
    return FieldDependency(
      parentFieldKey: parentKey,
      evaluate: condition,
    );
  }

  /// Evaluate this dependency against current form values
  bool shouldShow(Map<String, dynamic> formValues) {
    if (evaluate != null) {
      return evaluate!(formValues);
    }

    // Fallback to simple parentValue check
    return formValues[parentFieldKey.value] == parentValue;
  }
}

/// Type-safe form field configuration class
///
/// This class provides compile-time type safety for form field values and validation.
/// All validation constraints are consolidated into the validationRules list for consistency.
class FormFieldConfig<T> {
  final FormKeys key;
  final String label;
  final FormFieldType type;
  final double columnSpan; // 0.0 to 1.0 for responsive width
  final String? hint;
  final T? initialValue;
  final List<ValidationRule<T>> validationRules;
  final List<DropdownOption>? options;
  final Future<List<DropdownOption>> Function()? optionsLoader;
  final List<FieldDependency>? dependencies;
  final Map<String, dynamic>? customProperties;
  final IconData? icon;
  final bool enabled;
  final bool visible;
  final int? maxLines;
  final TextInputType? keyboardType;
  final DateTime? minDate; // For date fields only
  final DateTime? maxDate; // For date fields only
  final int? decimalPlaces; // For numeric fields only
  final String? suffix;
  final String? prefix;
  final bool autoFocus;
  final TextCapitalization textCapitalization;

  const FormFieldConfig({
    required this.key,
    required this.label,
    required this.type,
    this.columnSpan = 1.0,
    this.hint,
    this.initialValue,
    this.validationRules = const [],
    this.options,
    this.optionsLoader,
    this.dependencies,
    this.customProperties,
    this.icon,
    this.enabled = true,
    this.visible = true,
    this.maxLines = 1,
    this.keyboardType,
    this.minDate,
    this.maxDate,
    this.decimalPlaces,
    this.suffix,
    this.prefix,
    this.autoFocus = false,
    this.textCapitalization = TextCapitalization.none,
  });

  /// Convenience getters to extract validation constraints from rules
  /// These provide a clean API while keeping validationRules as the single source of truth

  /// Check if field is required
  bool get required => validationRules.any((rule) => rule.type == ValidationRuleType.required);

  /// Get minimum value constraint (for numeric fields)
  double? get minValue {
    final rule = validationRules.whereType<MinValueRule>().firstOrNull;
    return rule?.minValue.toDouble();
  }

  /// Get maximum value constraint (for numeric fields)
  double? get maxValue {
    final rule = validationRules.whereType<MaxValueRule>().firstOrNull;
    return rule?.maxValue.toDouble();
  }

  /// Get maximum length constraint (for text fields)
  int? get maxLength {
    final rule = validationRules.whereType<MaxLengthRule>().firstOrNull;
    return rule?.maxLength;
  }

  /// Get minimum length constraint (for text fields)
  int? get minLength {
    final rule = validationRules.whereType<MinLengthRule>().firstOrNull;
    return rule?.minLength;
  }

  /// Create a copy with modified properties (type-safe with explicit null handling)
  ///
  /// Use Value<T?>(null) to explicitly set a property to null.
  /// Example: config.copyWith(initialValue: Value<String?>(null))
  FormFieldConfig<T> copyWith({
    FormKeys? key,
    String? label,
    FormFieldType? type,
    double? columnSpan,
    Value<String?>? hint,
    Value<T?>? initialValue,
    List<ValidationRule<T>>? validationRules,
    List<DropdownOption>? options,
    Future<List<DropdownOption>> Function()? optionsLoader,
    List<FieldDependency>? dependencies,
    Map<String, dynamic>? customProperties,
    IconData? icon,
    bool? enabled,
    bool? visible,
    int? maxLines,
    TextInputType? keyboardType,
    DateTime? minDate,
    DateTime? maxDate,
    int? decimalPlaces,
    Value<String?>? suffix,
    Value<String?>? prefix,
    bool? autoFocus,
    TextCapitalization? textCapitalization,
  }) {
    return FormFieldConfig<T>(
      key: key ?? this.key,
      label: label ?? this.label,
      type: type ?? this.type,
      columnSpan: columnSpan ?? this.columnSpan,
      hint: hint != null ? hint.value : this.hint,
      initialValue: initialValue != null ? initialValue.value : this.initialValue,
      validationRules: validationRules ?? this.validationRules,
      options: options ?? this.options,
      optionsLoader: optionsLoader ?? this.optionsLoader,
      dependencies: dependencies ?? this.dependencies,
      customProperties: customProperties ?? this.customProperties,
      icon: icon ?? this.icon,
      enabled: enabled ?? this.enabled,
      visible: visible ?? this.visible,
      maxLines: maxLines ?? this.maxLines,
      keyboardType: keyboardType ?? this.keyboardType,
      minDate: minDate ?? this.minDate,
      maxDate: maxDate ?? this.maxDate,
      decimalPlaces: decimalPlaces ?? this.decimalPlaces,
      suffix: suffix != null ? suffix.value : this.suffix,
      prefix: prefix != null ? prefix.value : this.prefix,
      autoFocus: autoFocus ?? this.autoFocus,
      textCapitalization: textCapitalization ?? this.textCapitalization,
    );
  }

  /// Type-safe factory methods for common field types

  /// Create a text field configuration
  static FormFieldConfig<String> text({
    required FormKeys key,
    required String label,
    String? hint,
    String? initialValue,
    List<ValidationRule<String>> validationRules = const [],
    List<FieldDependency>? dependencies,
    IconData? icon,
    bool enabled = true,
    bool visible = true,
    int maxLines = 1,
    TextCapitalization textCapitalization = TextCapitalization.none,
    double columnSpan = 1.0,
  }) {
    return FormFieldConfig<String>(
      key: key,
      label: label,
      type: FormFieldType.text,
      hint: hint,
      initialValue: initialValue,
      validationRules: validationRules,
      dependencies: dependencies,
      icon: icon,
      enabled: enabled,
      visible: visible,
      maxLines: maxLines,
      textCapitalization: textCapitalization,
      columnSpan: columnSpan,
    );
  }

  /// Create a number field configuration
  static FormFieldConfig<double> number({
    required FormKeys key,
    required String label,
    String? hint,
    double? initialValue,
    List<ValidationRule<double>> validationRules = const [],
    List<FieldDependency>? dependencies,
    IconData? icon,
    bool enabled = true,
    bool visible = true,
    int? decimalPlaces,
    String? suffix,
    String? prefix,
    double columnSpan = 1.0,
  }) {
    return FormFieldConfig<double>(
      key: key,
      label: label,
      type: FormFieldType.number,
      hint: hint,
      initialValue: initialValue,
      validationRules: validationRules,
      dependencies: dependencies,
      icon: icon,
      enabled: enabled,
      visible: visible,
      decimalPlaces: decimalPlaces,
      suffix: suffix,
      prefix: prefix,
      columnSpan: columnSpan,
    );
  }

  /// Create a dropdown field configuration
  static FormFieldConfig<String> dropdown({
    required FormKeys key,
    required String label,
    String? hint,
    String? initialValue,
    List<ValidationRule<String>> validationRules = const [],
    List<DropdownOption>? options,
    Future<List<DropdownOption>> Function()? optionsLoader,
    List<FieldDependency>? dependencies,
    IconData? icon,
    bool enabled = true,
    bool visible = true,
    double columnSpan = 1.0,
  }) {
    return FormFieldConfig<String>(
      key: key,
      label: label,
      type: FormFieldType.dropdown,
      hint: hint,
      initialValue: initialValue,
      validationRules: validationRules,
      options: options,
      optionsLoader: optionsLoader,
      dependencies: dependencies,
      icon: icon,
      enabled: enabled,
      visible: visible,
      columnSpan: columnSpan,
    );
  }
}

/// Form section configuration for grouping fields
class FormSectionConfig {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final List<FormFieldConfig> fields; // Keep as non-generic for mixed field types
  final bool collapsible;
  final bool initiallyExpanded;

  const FormSectionConfig({
    required this.title,
    this.subtitle,
    this.icon,
    required this.fields,
    this.collapsible = false,
    this.initiallyExpanded = true,
  });
}

/// Complete form configuration with consistent section-based structure
///
/// This class enforces a clean, predictable structure where all forms use sections.
/// For simple forms, just provide a single FormSectionConfig with an empty title.
class FormConfig {
  final String title;
  final String? subtitle;
  final List<FormSectionConfig> sections; // Single source of truth for form structure
  final Map<String, dynamic>? initialValues;
  final bool scrollable;
  final double? maxWidth;
  final double? maxHeight;

  const FormConfig({
    required this.title,
    this.subtitle,
    required this.sections,
    this.initialValues,
    this.scrollable = true,
    this.maxWidth,
    this.maxHeight,
  });

  /// Convenience constructor for simple forms with a single section
  FormConfig.simple({
    required this.title,
    this.subtitle,
    required List<FormFieldConfig> fields,
    this.initialValues,
    this.scrollable = true,
    this.maxWidth,
    this.maxHeight,
  }) : sections = [
          FormSectionConfig(
            title: '', // Empty title for simple forms
            fields: fields,
          ),
        ];

  /// Get all fields from all sections (flattened)
  List<FormFieldConfig> get fields {
    return sections.expand((section) => section.fields).toList();
  }
}
