import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'dart:math';
import 'form_config.dart';

/// Utility class for form field operations
class FormFieldUtils {
  /// Get appropriate keyboard type based on field configuration
  static TextInputType getKeyboardType(FormFieldConfig config) {
    switch (config.type) {
      case FormFieldType.number:
      case FormFieldType.currency:
      case FormFieldType.percentage:
        return const TextInputType.numberWithOptions(decimal: true);
      case FormFieldType.phone:
        return TextInputType.phone;
      case FormFieldType.email:
        return TextInputType.emailAddress;
      case FormFieldType.url:
        return TextInputType.url;
      case FormFieldType.multiline:
        return TextInputType.multiline;
      default:
        return TextInputType.text;
    }
  }

  /// Get input formatters based on field configuration
  static List<TextInputFormatter> getInputFormatters(FormFieldConfig config) {
    final formatters = <TextInputFormatter>[];

    switch (config.type) {
      case FormFieldType.number:
      case FormFieldType.currency:
      case FormFieldType.percentage:
        formatters.add(FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')));
        break;
      case FormFieldType.phone:
        formatters.add(FilteringTextInputFormatter.allow(RegExp(r'[0-9+\-\s\(\)]')));
        break;
      default:
        break;
    }

    return formatters;
  }

  /// Parse string value according to field type
  static dynamic parseValue(String value, FormFieldConfig config) {
    if (value.isEmpty) return null;

    switch (config.type) {
      case FormFieldType.number:
      case FormFieldType.currency:
      case FormFieldType.percentage:
        return double.tryParse(value);
      case FormFieldType.date:
        try {
          return DateTime.parse(value);
        } catch (e) {
          return null;
        }
      default:
        return value;
    }
  }

  /// Format value for display in text field
  static String formatValueForDisplay(dynamic value, FormFieldConfig config) {
    if (value == null) return '';

    switch (config.type) {
      case FormFieldType.date:
        if (value is DateTime) {
          return DateFormat('yyyy-MM-dd').format(value);
        }
        break;
      case FormFieldType.currency:
        if (value is num) {
          return NumberFormat.simpleCurrency(decimalDigits: config.decimalPlaces ?? 2).format(value);
        }
        break;
      case FormFieldType.percentage:
        if (value is num) {
          final decimalPlaces = config.decimalPlaces ?? 2;
          return '${(value * 100).toStringAsFixed(decimalPlaces)}%';
        }
        break;
      case FormFieldType.number:
        if (value is num) {
          final decimalPlaces = config.decimalPlaces ?? 2;
          return value.toStringAsFixed(decimalPlaces);
        }
        break;
      default:
        return value.toString();
    }

    return value.toString();
  }
}

/// Extension on FormFieldConfig providing type-safe form field utilities
/// All logic is contained directly in the extension for clean architecture
extension FormFieldConfigExtensions on FormFieldConfig {

  /// Format a numeric value for display using this field's configuration
  String formatValue(double value) {
    final decimalPlaces = this.decimalPlaces ?? 2;

    switch (this.type) {
      case FormFieldType.percentage:
        return '${(value * 100).toStringAsFixed(decimalPlaces)}%';
      case FormFieldType.currency:
        // Use intl package for proper currency formatting
        return NumberFormat.simpleCurrency(decimalDigits: decimalPlaces).format(value);
      default:
        return value.toStringAsFixed(decimalPlaces);
    }
  }

  /// Get slider divisions for this field's configuration
  /// Uses a simplified, predictable algorithm based on decimal places
  int? get sliderDivisions {
    final minValue = this.minValue ?? 0.0;
    final maxValue = this.maxValue ?? 1.0;

    // For percentage sliders, use 100 divisions for intuitive 1% steps
    if (this.type == FormFieldType.percentage) {
      return 100;
    }

    // Calculate divisions based on desired precision
    if (this.decimalPlaces == null || this.decimalPlaces == 0) {
      return (maxValue - minValue).round();
    }

    final factor = pow(10, this.decimalPlaces!);
    return ((maxValue - minValue) * factor).round();
  }

  /// Get initial slider value with proper type-safe fallbacks
  double getInitialSliderValue(dynamic controllerValue) {
    return (controllerValue as double?) ??
           (this.initialValue as double?) ??
           (this.minValue ?? 0.0);
  }

  /// Clamp value to this field's bounds
  double clampValue(double value) {
    final minValue = this.minValue ?? 0.0;
    final maxValue = this.maxValue ?? 1.0;
    return value.clamp(minValue, maxValue);
  }

  /// Get appropriate keyboard type based on field type
  TextInputType get keyboardType {
    switch (this.type) {
      case FormFieldType.number:
      case FormFieldType.currency:
      case FormFieldType.percentage:
        return const TextInputType.numberWithOptions(decimal: true);
      case FormFieldType.phone:
        return TextInputType.phone;
      case FormFieldType.email:
        return TextInputType.emailAddress;
      case FormFieldType.url:
        return TextInputType.url;
      case FormFieldType.multiline:
        return TextInputType.multiline;
      default:
        return TextInputType.text;
    }
  }

  /// Get input formatters based on field type with improved numeric handling
  List<TextInputFormatter> get inputFormatters {
    final formatters = <TextInputFormatter>[];

    switch (this.type) {
      case FormFieldType.number:
      case FormFieldType.currency:
      case FormFieldType.percentage:
        // Improved regex for decimal numbers: allows digits, optional dot, up to 2 decimal places
        formatters.add(FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')));
        break;
      case FormFieldType.phone:
        formatters.add(FilteringTextInputFormatter.allow(RegExp(r'[\d\s\-\(\)\+]')));
        break;
      default:
        break;
    }

    // Add length restrictions if specified
    final maxLengthRule = this.validationRules.whereType<MaxLengthRule>().firstOrNull;
    if (maxLengthRule != null) {
      formatters.add(LengthLimitingTextInputFormatter(maxLengthRule.maxLength));
    }

    return formatters;
  }

  /// Parse string value according to this field's type with proper type safety
  /// Returns the correctly typed value or null if parsing fails
  T? parseValue<T>(String value) {
    if (value.isEmpty) return null;

    switch (this.type) {
      case FormFieldType.number:
      case FormFieldType.currency:
      case FormFieldType.percentage:
        final parsed = double.tryParse(value);
        if (parsed != null) {
          return parsed as T?;
        }
        return null;

      case FormFieldType.date:
        try {
          final parsed = DateTime.parse(value);
          return parsed as T?;
        } catch (e) {
          return null;
        }

      default:
        // For text-based fields, expect String type
        return value as T?;
    }
  }

  /// Format value for display in text field with type safety
  /// Takes the correctly typed value and formats it for display
  String formatValueForDisplay<T>(T? value) {
    if (value == null) return '';

    switch (this.type) {
      case FormFieldType.date:
        if (value is DateTime) {
          return DateFormat('yyyy-MM-dd').format(value);
        }
        assert(value is DateTime,
          'Type mismatch in formatValueForDisplay: Expected DateTime for date field "${this.label}", '
          'but got ${value.runtimeType}. Use FormFieldConfig<DateTime> for date fields.');
        break;

      case FormFieldType.currency:
        if (value is double) {
          return NumberFormat.simpleCurrency(decimalDigits: this.decimalPlaces ?? 2).format(value);
        }
        if (value is int) {
          return NumberFormat.simpleCurrency(decimalDigits: this.decimalPlaces ?? 2).format(value.toDouble());
        }
        assert(value is num,
          'Type mismatch in formatValueForDisplay: Expected num for currency field "${this.label}", '
          'but got ${value.runtimeType}. Use FormFieldConfig<double> for currency fields.');
        break;

      case FormFieldType.percentage:
        if (value is double) {
          final decimalPlaces = this.decimalPlaces ?? 2;
          return '${(value * 100).toStringAsFixed(decimalPlaces)}%';
        }
        if (value is int) {
          final decimalPlaces = this.decimalPlaces ?? 2;
          return '${(value.toDouble() * 100).toStringAsFixed(decimalPlaces)}%';
        }
        assert(value is num,
          'Type mismatch in formatValueForDisplay: Expected num for percentage field "${this.label}", '
          'but got ${value.runtimeType}. Use FormFieldConfig<double> for percentage fields.');
        break;

      case FormFieldType.number:
        if (value is double) {
          final decimalPlaces = this.decimalPlaces ?? 2;
          return value.toStringAsFixed(decimalPlaces);
        }
        if (value is int) {
          return value.toString();
        }
        assert(value is num,
          'Type mismatch in formatValueForDisplay: Expected num for number field "${this.label}", '
          'but got ${value.runtimeType}. Use FormFieldConfig<double> or FormFieldConfig<int> for number fields.');
        break;

      default:
        // For text-based fields, expect String type
        if (value is String) {
          return value;
        }
        assert(value is String,
          'Type mismatch in formatValueForDisplay: Expected String for text field "${this.label}", '
          'but got ${value.runtimeType}. Use FormFieldConfig<String> for text-based fields.');
        break;
    }

    // Fallback to toString() if type checks fail
    return value.toString();
  }

  /// Check if this field accepts numeric input
  bool get isNumericField {
    return [
      FormFieldType.number,
      FormFieldType.currency,
      FormFieldType.percentage,
    ].contains(this.type);
  }

  /// Check if this field is a text-based field
  bool get isTextField {
    return [
      FormFieldType.text,
      FormFieldType.email,
      FormFieldType.phone,
      FormFieldType.url,
      FormFieldType.multiline,
    ].contains(this.type);
  }

  /// Get the step value for numeric sliders
  double get sliderStep {
    if (this.decimalPlaces == null || this.decimalPlaces == 0) {
      return 1.0;
    }
    return 1.0 / pow(10, this.decimalPlaces!);
  }

  /// Get a user-friendly label for validation errors
  String get fieldLabel {
    return this.label.isNotEmpty ? this.label : 'Field';
  }

  /// Check if this field has a specific validation rule
  bool hasValidationRule(ValidationRuleType ruleType) {
    return this.validationRules.any((rule) => rule.type == ruleType);
  }

  /// Get the value of a specific validation rule
  dynamic getValidationRuleValue(ValidationRuleType ruleType) {
    try {
      final rule = this.validationRules.firstWhere((rule) => rule.type == ruleType);
      switch (ruleType) {
        case ValidationRuleType.minValue:
          return (rule as MinValueRule).minValue;
        case ValidationRuleType.maxValue:
          return (rule as MaxValueRule).maxValue;
        case ValidationRuleType.minLength:
          return (rule as MinLengthRule).minLength;
        case ValidationRuleType.maxLength:
          return (rule as MaxLengthRule).maxLength;
        default:
          return null;
      }
    } catch (e) {
      return null;
    }
  }

  /// Get maximum length for text fields
  int? get maxLength {
    if (hasValidationRule(ValidationRuleType.maxLength)) {
      return getValidationRuleValue(ValidationRuleType.maxLength) as int?;
    }
    return null;
  }

  /// Get minimum length for text fields
  int? get minLength {
    if (hasValidationRule(ValidationRuleType.minLength)) {
      return getValidationRuleValue(ValidationRuleType.minLength) as int?;
    }
    return null;
  }

  /// Check if this field is required
  bool get isRequired {
    return hasValidationRule(ValidationRuleType.required);
  }

  /// Validate that a string value is properly parseable for this field type
  /// Returns true if valid, false if invalid
  bool isValidValue(String value) {
    if (value.isEmpty) {
      return !isRequired; // Empty is valid only if field is not required
    }

    switch (this.type) {
      case FormFieldType.number:
      case FormFieldType.currency:
      case FormFieldType.percentage:
        // For numeric fields, ensure the value can be parsed as a double
        final parsed = double.tryParse(value);
        if (parsed == null) return false;

        // Check bounds if specified
        if (this.minValue != null && parsed < this.minValue!) return false;
        if (this.maxValue != null && parsed > this.maxValue!) return false;

        return true;

      case FormFieldType.email:
        // Basic email validation
        return RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(value);

      case FormFieldType.phone:
        // Basic phone validation (digits, spaces, dashes, parentheses, plus)
        return RegExp(r'^[\d\s\-\(\)\+]+$').hasMatch(value);

      case FormFieldType.url:
        // Basic URL validation
        return Uri.tryParse(value) != null;

      case FormFieldType.date:
        // Date validation
        try {
          DateTime.parse(value);
          return true;
        } catch (e) {
          return false;
        }

      default:
        // For text fields, check length constraints
        if (this.minLength != null && value.length < this.minLength!) return false;
        if (this.maxLength != null && value.length > this.maxLength!) return false;

        return true;
    }
  }

  /// Get a user-friendly error message for invalid values
  String getValidationErrorMessage(String value) {
    if (value.isEmpty && isRequired) {
      return '${this.fieldLabel} is required';
    }

    if (value.isNotEmpty && !isValidValue(value)) {
      switch (this.type) {
        case FormFieldType.number:
        case FormFieldType.currency:
        case FormFieldType.percentage:
          return '${this.fieldLabel} must be a valid number';
        case FormFieldType.email:
          return '${this.fieldLabel} must be a valid email address';
        case FormFieldType.phone:
          return '${this.fieldLabel} must be a valid phone number';
        case FormFieldType.url:
          return '${this.fieldLabel} must be a valid URL';
        case FormFieldType.date:
          return '${this.fieldLabel} must be a valid date';
        default:
          if (this.minLength != null && value.length < this.minLength!) {
            return '${this.fieldLabel} must be at least ${this.minLength} characters';
          }
          if (this.maxLength != null && value.length > this.maxLength!) {
            return '${this.fieldLabel} must be no more than ${this.maxLength} characters';
          }
          return '${this.fieldLabel} is invalid';
      }
    }

    return ''; // No error
  }

  /// Advanced copyWith method for validation rules
  /// Allows easy modification of validation rules without recreating the entire config
  FormFieldConfig copyWithValidationRules(List<ValidationRule> newRules) {
    return FormFieldConfig(
      key: this.key,
      label: this.label,
      type: this.type,
      columnSpan: this.columnSpan,
      hint: this.hint,
      initialValue: this.initialValue,
      validationRules: newRules,
      options: this.options,
      optionsLoader: this.optionsLoader,
      dependencies: this.dependencies,
      customProperties: this.customProperties,
      icon: this.icon,
      enabled: this.enabled,
      visible: this.visible,
      maxLines: this.maxLines,
      decimalPlaces: this.decimalPlaces,
      textCapitalization: this.textCapitalization,
    );
  }

  /// Add a validation rule to this field configuration
  FormFieldConfig addValidationRule(ValidationRule rule) {
    final newRules = List<ValidationRule>.from(this.validationRules)..add(rule);
    return copyWithValidationRules(newRules);
  }

  /// Remove a validation rule type from this field configuration
  FormFieldConfig removeValidationRule(ValidationRuleType ruleType) {
    final newRules = this.validationRules.where((rule) => rule.type != ruleType).toList();
    return copyWithValidationRules(newRules);
  }
}
