# Standardized Form Widget Guide

## Overview

The **Universal Form System** provides a single, standardized UI and layout for all forms in the Cattle Manager App. Based on the pregnancy form dialog as the reference standard, this system ensures consistent user experience across all modules.

## Standard UI Specifications

### Responsive Dialog Dimensions
- **Mobile (< 600px)**: Full width minus 32px padding, max height = screen height - 100px
- **Tablet (600-1200px)**: Fixed 600px width, 700px max height
- **Desktop (> 1200px)**: Fixed 800px width, 700px max height
- **Border Radius**: 8px on mobile, 12px on larger screens
- **Shadow**: Subtle drop shadow with 20px blur radius

### Responsive Breakpoints
- **Mobile**: < 480px - Single column, compact spacing
- **Tablet**: 480-768px - 1-2 columns, medium spacing
- **Desktop**: 768-1200px - 2-3 columns, standard spacing
- **Wide**: > 1200px - 3+ columns, generous spacing

### Responsive Layout Structure

#### Desktop/Tablet Layout
```
┌─────────────────────────────────────────────────────────┐
│ HEADER (Title centered, Modified chip on right)        │
├─────────────────────────────────────────────────────────┤
│ BODY (Multi-column responsive form fields)             │
│ ┌─────────────────┬─────────────────┬─────────────────┐ │
│ │ Field 1 (33%)   │ Field 2 (33%)   │ Field 3 (33%)   │ │
│ ├─────────────────┴─────────────────┴─────────────────┤ │
│ │ Field 4 (Full width)                                │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ FOOTER (Cancel left, Save right)                       │
└─────────────────────────────────────────────────────────┘
```

#### Mobile Layout
```
┌─────────────────────────────────────┐
│ HEADER (Title + Modified stacked)   │
├─────────────────────────────────────┤
│ BODY (Single column layout)         │
│ ┌─────────────────────────────────┐ │
│ │ Field 1 (Full width)            │ │
│ │ Field 2 (Full width)            │ │
│ │ Field 3 (Full width)            │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ FOOTER (Save + Cancel stacked)      │
└─────────────────────────────────────┘
```

### Color Standards
- **Header Background**: Module-specific theme color (from UiThemeService)
- **Header Text**: White with bold font weight
- **Field Icons**: Contextual colors (brown for cattle, green for dates, etc.)
- **Buttons**: Theme color for save, default for cancel

### Responsive Spacing Standards

#### Mobile (< 600px)
- **Header Padding**: 12px all around
- **Body Padding**: 12px all around
- **Field Spacing**: 12px between form fields
- **Footer Padding**: 12px all around
- **Button Layout**: Stacked (Save on top, Cancel below)

#### Tablet (600-1200px)
- **Header Padding**: 16px all around
- **Body Padding**: 16px all around
- **Field Spacing**: 16px between form fields
- **Footer Padding**: 16px all around
- **Button Spacing**: 12px between buttons

#### Desktop (> 1200px)
- **Header Padding**: 16px all around
- **Body Padding**: 20px all around
- **Field Spacing**: 20px between form fields
- **Footer Padding**: 16px all around
- **Button Spacing**: 16px between buttons

### Field Standards
- **Border Radius**: 8px for all form fields
- **Content Padding**: 12px horizontal, 16px vertical (matches pregnancy form)
- **Icon Size**: 24px with contextual colors
- **Label Style**: Floating labels with proper spacing
- **Validation**: Real-time validation with error messages

### Responsive Column Behavior
- **Mobile**: All fields take full width (columnSpan = 1.0) except checkboxes (0.5)
- **Tablet**: Respects original columnSpan but caps at 1.0 for very wide fields
- **Desktop**: Uses original columnSpan values as specified
- **Wide Screens**: Allows smaller minimum spans (0.25) for more columns

### Field Width Constraints
- **Mobile**: Minimum 120px width per field
- **Tablet**: Minimum 150px width per field
- **Desktop**: Minimum 180px width per field
- **Wide**: Minimum 200px width per field

## Usage Examples

### Basic Form Dialog
```dart
import 'package:flutter/material.dart';
import '../widgets/dialogs/index.dart'; // Universal form system

// Show a standardized form dialog
final result = await UniversalFormDialog.show(
  context: context,
  config: FormConfig.simple(
    title: 'Add Weight Record',
    fields: [
      FormFieldConfig<String>(
        key: FormKeys.cattleId,
        label: 'Cattle',
        type: FormFieldType.dropdown,
        icon: Icons.pets,
        validationRules: [RequiredRule<String>()],
        optionsLoader: () => _loadCattleOptions(),
      ),
      FormFieldConfig<double>(
        key: FormKeys.weight,
        label: 'Weight (kg)',
        type: FormFieldType.number,
        icon: Icons.monitor_weight,
        validationRules: [
          RequiredRule<double>(),
          MinValueRule(0.0),
          MaxValueRule(2000.0),
        ],
      ),
      FormFieldConfig<DateTime>(
        key: FormKeys.date,
        label: 'Date',
        type: FormFieldType.date,
        icon: Icons.calendar_today,
        initialValue: DateTime.now(),
      ),
    ],
  ),
  section: 'weight', // For theming
  onSave: (values) => _saveRecord(values),
);
```

### Advanced Form with Dependencies
```dart
final result = await UniversalFormDialog.show(
  context: context,
  config: FormConfig.simple(
    title: 'Pregnancy Record',
    fields: [
      FormFieldConfig<String>(
        key: FormKeys.cattleId,
        label: 'Cattle',
        type: FormFieldType.dropdown,
        icon: Icons.pets,
        validationRules: [RequiredRule<String>()],
        optionsLoader: () => _loadFemaleCattle(),
      ),
      FormFieldConfig<String>(
        key: FormKeys.status,
        label: 'Status',
        type: FormFieldType.dropdown,
        icon: Icons.check_circle,
        options: [
          DropdownOption('confirmed', 'Confirmed'),
          DropdownOption('completed', 'Completed'),
          DropdownOption('abortion', 'Abortion'),
        ],
        dependencies: [
          FieldDependency(
            dependsOn: FormKeys.status,
            condition: (value) => value == 'completed',
            action: DependencyAction.show,
            targetFields: [FormKeys.calvingDate],
          ),
        ],
      ),
      FormFieldConfig<DateTime>(
        key: FormKeys.calvingDate,
        label: 'Calving Date',
        type: FormFieldType.date,
        icon: Icons.event_available,
        visible: false, // Initially hidden
      ),
    ],
  ),
  section: 'breeding',
  onSave: (values) => _savePregnancyRecord(values),
);
```

## Migration from Legacy Forms

### Step 1: Identify Form Fields
Analyze your existing form dialog and list all fields:
- Field types (text, dropdown, date, checkbox, etc.)
- Validation rules
- Dependencies between fields
- Icons and styling

### Step 2: Create Form Configuration
Replace hardcoded form widgets with FormFieldConfig objects:

**Before (Legacy)**:
```dart
TextFormField(
  decoration: InputDecoration(
    labelText: 'Weight (kg)',
    border: OutlineInputBorder(),
    prefixIcon: Icon(Icons.monitor_weight),
  ),
  validator: (value) => value?.isEmpty == true ? 'Required' : null,
  onSaved: (value) => _weight = double.tryParse(value ?? ''),
)
```

**After (Universal)**:
```dart
FormFieldConfig<double>(
  key: FormKeys.weight,
  label: 'Weight (kg)',
  type: FormFieldType.number,
  icon: Icons.monitor_weight,
  validationRules: [RequiredRule<double>()],
)
```

### Step 3: Replace Dialog Usage
Replace your custom dialog with UniversalFormDialog.show():

**Before**:
```dart
showDialog(
  context: context,
  builder: (context) => WeightFormDialog(cattle: cattle),
);
```

**After**:
```dart
UniversalFormDialog.show(
  context: context,
  config: FormConfig.simple(
    title: 'Weight Record',
    fields: ModuleFormConfigs().forWeight(),
  ),
  initialValues: {'cattle': cattle.id},
  section: 'weight',
  onSave: (values) => _saveWeight(values),
);
```

## Benefits of Standardization

✅ **Consistent UI**: All forms look and behave identically  
✅ **Reduced Code**: 80% less boilerplate code per form  
✅ **Better UX**: Responsive layouts, proper validation, accessibility  
✅ **Maintainability**: Single source of truth for form styling  
✅ **Type Safety**: Compile-time validation of form configurations  
✅ **Advanced Features**: Field dependencies, dynamic options, caching  

## Form Field Types Supported

- **Text Fields**: Single/multi-line text input
- **Number Fields**: Integer and decimal number input
- **Dropdown Fields**: Single selection with static or dynamic options
- **Date Fields**: Date picker with calendar widget
- **Checkbox Fields**: Boolean toggle inputs
- **Slider Fields**: Numeric range selection

## Validation Rules Available

- **RequiredRule**: Field must have a value
- **MinLengthRule**: Minimum text length
- **MaxLengthRule**: Maximum text length
- **MinValueRule**: Minimum numeric value
- **MaxValueRule**: Maximum numeric value
- **EmailRule**: Valid email format
- **PhoneRule**: Valid phone number format
- **CustomRule**: Custom validation logic

## Next Steps

1. **Review existing forms** in your module
2. **Create FormFieldConfig** for each field
3. **Add configurations** to ModuleFormConfigs
4. **Replace dialog calls** with UniversalFormDialog.show
5. **Test thoroughly** to ensure functionality is preserved
6. **Remove legacy files** once migration is complete

The Universal Form System is your **one UI and layout for all forms** - use it consistently across all modules for the best user experience.
