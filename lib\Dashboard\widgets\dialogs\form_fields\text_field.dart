import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../form_config.dart';
import '../universal_form_controller.dart';
import '../config/ui_theme_service.dart';
import '../form_field_utils.dart';

/// Universal text field widget for the form system
///
/// This is an enterprise-grade text field component that provides:
/// - Efficient rebuilds with ListenableBuilder (only rebuilds on error state changes)
/// - Robust TextEditingController lifecycle management (prevents memory leaks)
/// - Debounced input for optimal performance (300ms delay for validation)
/// - Automatic validation feedback with autovalidateMode
/// - Clean separation of concerns (UI logic vs business logic)
/// - Support for all text input types (text, number, email, phone, etc.)
class UniversalTextField extends StatefulWidget {
  final UniversalFormController controller;
  final FormFieldConfig config;
  final Color themeColor;

  const UniversalTextField({
    Key? key,
    required this.controller,
    required this.config,
    required this.themeColor,
  }) : super(key: key);

  @override
  State<UniversalTextField> createState() => _UniversalTextFieldState();
}

class _UniversalTextFieldState extends State<UniversalTextField> {
  late final TextEditingController _textController;
  Timer? _debounce;

  /// Getter for cleaner field key access
  String get fieldKey => widget.config.key.value;

  @override
  void initState() {
    super.initState();

    // Initialize text controller with current value from form controller
    final currentValue = widget.controller.getValue(fieldKey);
    _textController = TextEditingController(
      text: _formatValueForDisplay(currentValue),
    );

    // Listen to form controller for external changes (like form reset)
    widget.controller.addListener(_handleControllerChange);
  }

  @override
  void didUpdateWidget(UniversalTextField oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle controller instance changes
    if (widget.controller != oldWidget.controller) {
      oldWidget.controller.removeListener(_handleControllerChange);
      widget.controller.addListener(_handleControllerChange);

      // Update text controller with new controller's value
      final currentValue = widget.controller.getValue(fieldKey);
      _textController.text = _formatValueForDisplay(currentValue);
    }
  }

  @override
  void dispose() {
    _debounce?.cancel();
    widget.controller.removeListener(_handleControllerChange);
    _textController.dispose();
    super.dispose();
  }

  /// Handle external changes from form controller
  ///
  /// This method syncs the local TextEditingController with external changes
  /// from the main form controller (e.g., form reset, programmatic value changes).
  ///
  /// Note: This works in conjunction with ListenableBuilder for error updates.
  /// The listener handles value synchronization, while ListenableBuilder handles
  /// error state updates for optimal performance.
  void _handleControllerChange() {
    final controllerValue = widget.controller.getValue(fieldKey);
    final displayValue = _formatValueForDisplay(controllerValue);

    // Only update if the text controller's value is different
    // This prevents infinite loops and unnecessary cursor position changes
    if (_textController.text != displayValue) {
      _textController.text = displayValue;
    }
  }

  /// Format value for display in text field
  String _formatValueForDisplay(dynamic value) {
    if (value == null) return '';
    return FormFieldUtils.formatValueForDisplay(value, widget.config);
  }

  @override
  Widget build(BuildContext context) {
    final uiTheme = UiThemeService();

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        final errorText = widget.controller.validationErrors[fieldKey];

        return TextFormField(
          controller: _textController,
          enabled: widget.config.enabled,
          autofocus: widget.config.autoFocus,
          maxLines: widget.config.maxLines,
          keyboardType: widget.config.keyboardType ?? _getKeyboardType(),
          textCapitalization: widget.config.textCapitalization,
          inputFormatters: _getInputFormatters(),
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: uiTheme.getFormFieldDecoration(
            label: widget.config.label,
            hint: widget.config.hint,
            icon: widget.config.icon,
            themeColor: widget.themeColor,
            errorText: errorText,
            prefix: widget.config.prefix,
            suffix: widget.config.suffix,
            isRequired: widget.config.validationRules.any((rule) => rule.type == ValidationRuleType.required),
          ),
          validator: (value) => errorText,
          onChanged: _handleValueChange,
          onFieldSubmitted: _handleValueChangeImmediate,
        );
      },
    );
  }

  /// Get appropriate keyboard type based on field type
  TextInputType _getKeyboardType() {
    return FormFieldUtils.getKeyboardType(widget.config);
  }

  /// Get input formatters based on field type
  List<TextInputFormatter> _getInputFormatters() {
    return FormFieldUtils.getInputFormatters(widget.config);
  }

  /// Handle value changes with debouncing (for performance)
  ///
  /// This method provides optimal UX by:
  /// 1. Giving immediate visual feedback via TextEditingController
  /// 2. Debouncing expensive operations (validation, state updates) by 300ms
  /// 3. Reducing validation overhead during active typing
  void _handleValueChange(String value) {
    // Cancel any existing debounce timer
    _debounce?.cancel();

    // Start a new debounce timer (300ms is optimal for most users)
    _debounce = Timer(const Duration(milliseconds: 300), () {
      _commitValueToController(value);
    });
  }

  /// Handle immediate value changes (for onFieldSubmitted)
  void _handleValueChangeImmediate(String value) {
    _debounce?.cancel();
    _commitValueToController(value);
  }

  /// Commit the parsed value to the form controller
  void _commitValueToController(String value) {
    final parsedValue = FormFieldUtils.parseValue(value, widget.config);
    widget.controller.setValue(fieldKey, parsedValue);
  }
}
