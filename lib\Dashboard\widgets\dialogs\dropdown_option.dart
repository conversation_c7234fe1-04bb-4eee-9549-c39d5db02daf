import 'package:flutter/material.dart';

/// Wrapper class for nullable values in copyWith methods
/// Allows explicit setting of nullable properties to null
class Value<T> {
  final T value;
  const Value(this.value);
}

/// Configuration for dropdown field options
/// This is a reusable model that can be used across the entire application
/// Uses manual equality implementation for robust value equality and immutability
class DropdownOption {
  final String value;
  final String label;
  final IconData? icon;
  final Color? color;
  final bool enabled;
  final Map<String, dynamic>? metadata;

  const DropdownOption({
    required this.value,
    required this.label,
    this.icon,
    this.color,
    this.enabled = true,
    this.metadata,
  });

  /// Create a copy with modified properties
  /// Uses Value wrapper for nullable properties to allow explicit null setting
  DropdownOption copyWith({
    String? value,
    String? label,
    Value<IconData?>? icon,
    Value<Color?>? color,
    bool? enabled,
    Value<Map<String, dynamic>?>? metadata,
  }) {
    return DropdownOption(
      value: value ?? this.value,
      label: label ?? this.label,
      icon: icon != null ? icon.value : this.icon,
      color: color != null ? color.value : this.color,
      enabled: enabled ?? this.enabled,
      metadata: metadata != null ? metadata.value : this.metadata,
    );
  }

  /// Convert to JSON with robust serialization
  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'label': label,
      'enabled': enabled,
      if (icon != null) 'iconCodePoint': icon!.codePoint,
      if (color != null) 'colorValue': (color!.a.toInt() << 24) | (color!.r.toInt() << 16) | (color!.g.toInt() << 8) | color!.b.toInt(),
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create from JSON with error handling
  factory DropdownOption.fromJson(Map<String, dynamic> json) {
    try {
      return DropdownOption(
        value: json['value'] as String? ?? 
               (throw const FormatException('Missing required field: value')),
        label: json['label'] as String? ?? 
               (throw const FormatException('Missing required field: label')),
        enabled: json['enabled'] as bool? ?? true,
        icon: json['iconCodePoint'] != null 
            ? IconData(json['iconCodePoint'] as int, fontFamily: 'MaterialIcons')
            : null,
        color: json['colorValue'] != null 
            ? Color(json['colorValue'] as int)
            : null,
        metadata: json['metadata'] as Map<String, dynamic>?,
      );
    } catch (e) {
      throw FormatException('Failed to parse DropdownOption from JSON: $e');
    }
  }

  /// Manual equality implementation for robust value equality
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DropdownOption &&
          runtimeType == other.runtimeType &&
          value == other.value &&
          label == other.label &&
          icon == other.icon &&
          color == other.color &&
          enabled == other.enabled &&
          metadata == other.metadata;

  @override
  int get hashCode => Object.hash(value, label, icon, color, enabled, metadata);

  @override
  String toString() {
    return 'DropdownOption(value: $value, label: $label, enabled: $enabled, '
           'icon: $icon, color: $color, metadata: $metadata)';
  }
}

/// Simple utility enums that don't conflict with form_enums.dart
/// For main business enums, use form_enums.dart

/// Yes/No options with type safety
enum YesNo {
  yes('Yes', Icons.check),
  no('No', Icons.close);

  const YesNo(this.label, this.icon);
  final String label;
  final IconData icon;

  String get value => name;

  DropdownOption toDropdownOption() {
    return DropdownOption(value: value, label: label, icon: icon);
  }

  static List<DropdownOption> get dropdownOptions {
    return values.map((option) => option.toDropdownOption()).toList();
  }

  static YesNo? tryParse(String value) {
    try {
      return values.firstWhere((option) => option.value == value);
    } catch (e) {
      return null;
    }
  }

  /// Convenience getters
  bool get isYes => this == YesNo.yes;
  bool get isNo => this == YesNo.no;
}

/// Quality rating options with type safety
enum Quality {
  excellent('Excellent', Icons.star),
  good('Good', Icons.star_half),
  average('Average', Icons.star_border),
  poor('Poor', Icons.star_outline);

  const Quality(this.label, this.icon);
  final String label;
  final IconData icon;

  String get value => name;

  DropdownOption toDropdownOption() {
    return DropdownOption(value: value, label: label, icon: icon);
  }

  static List<DropdownOption> get dropdownOptions {
    return values.map((quality) => quality.toDropdownOption()).toList();
  }

  static Quality? tryParse(String value) {
    try {
      return values.firstWhere((quality) => quality.value == value);
    } catch (e) {
      return null;
    }
  }

  /// Convenience methods for business logic
  bool get isGoodOrBetter => index <= Quality.good.index;
  bool get isPoorOrWorse => index >= Quality.poor.index;
  int get rating => values.length - index; // 4=excellent, 1=poor
}

