import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../form_config.dart';
import '../universal_form_controller.dart';
import '../config/ui_theme_service.dart';

/// Universal date field widget for the form system
class UniversalDateField extends StatelessWidget {
  final UniversalFormController controller;
  final FormFieldConfig config;
  final Color themeColor;

  const UniversalDateField({
    Key? key,
    required this.controller,
    required this.config,
    required this.themeColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final uiTheme = UiThemeService();
    final currentValue = controller.getValue(config.key.value) as DateTime?;
    final errorText = controller.validationErrors[config.key.value];

    return FormField<DateTime>(
      initialValue: currentValue,
      validator: (value) => errorText,
      builder: (FormFieldState<DateTime> field) {
        return InkWell(
          onTap: config.enabled ? () => _selectDate(context, field) : null,
          borderRadius: BorderRadius.circular(8),
          child: InputDecorator(
            decoration: uiTheme.getFormFieldDecoration(
              label: config.label,
              hint: config.hint ?? 'Select date',
              icon: config.icon ?? Icons.calendar_today,
              themeColor: themeColor,
              errorText: field.errorText,
              prefix: config.prefix,
              suffix: config.suffix,
              isRequired: config.validationRules.any((rule) => rule.type == ValidationRuleType.required),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _formatDate(currentValue),
                    style: TextStyle(
                      color: currentValue != null
                          ? Theme.of(context).textTheme.bodyLarge?.color
                          : Colors.grey[600],
                      fontSize: 14, // Reduced from 16 to 14 to help with label clipping
                    ),
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  color: config.enabled ? themeColor : Colors.grey,
                  size: 20,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Format date for display
  String _formatDate(DateTime? date) {
    if (date == null) {
      return config.hint ?? 'Select date';
    }

    // Use different formats based on field type
    switch (config.type) {
      case FormFieldType.date:
        return DateFormat('MMMM dd, yyyy').format(date);
      case FormFieldType.dateTime:
        return DateFormat('MMMM dd, yyyy HH:mm').format(date);
      case FormFieldType.time:
        return DateFormat('HH:mm').format(date);
      default:
        return DateFormat('MMMM dd, yyyy').format(date);
    }
  }

  /// Show date picker
  Future<void> _selectDate(BuildContext context, FormFieldState<DateTime> field) async {
    final currentValue = controller.getValue(config.key.value) as DateTime?;
    final initialDate = currentValue ?? DateTime.now();
    
    // Determine date range
    final firstDate = config.minDate ?? DateTime(1900);
    final lastDate = config.maxDate ?? DateTime(2100);

    DateTime? selectedDate;

    switch (config.type) {
      case FormFieldType.date:
        selectedDate = await showDatePicker(
          context: context,
          initialDate: _clampDate(initialDate, firstDate, lastDate),
          firstDate: firstDate,
          lastDate: lastDate,
          builder: (context, child) => _buildDatePickerTheme(context, child),
        );
        break;

      case FormFieldType.dateTime:
        // First select date
        final date = await showDatePicker(
          context: context,
          initialDate: _clampDate(initialDate, firstDate, lastDate),
          firstDate: firstDate,
          lastDate: lastDate,
          builder: (context, child) => _buildDatePickerTheme(context, child),
        );

        if (date != null && context.mounted) {
          // Then select time
          final time = await showTimePicker(
            context: context,
            initialTime: TimeOfDay.fromDateTime(currentValue ?? DateTime.now()),
            builder: (context, child) => _buildTimePickerTheme(context, child),
          );

          if (time != null) {
            selectedDate = DateTime(
              date.year,
              date.month,
              date.day,
              time.hour,
              time.minute,
            );
          }
        }
        break;

      case FormFieldType.time:
        final time = await showTimePicker(
          context: context,
          initialTime: TimeOfDay.fromDateTime(currentValue ?? DateTime.now()),
          builder: (context, child) => _buildTimePickerTheme(context, child),
        );

        if (time != null) {
          final now = DateTime.now();
          selectedDate = DateTime(
            now.year,
            now.month,
            now.day,
            time.hour,
            time.minute,
          );
        }
        break;

      default:
        selectedDate = await showDatePicker(
          context: context,
          initialDate: _clampDate(initialDate, firstDate, lastDate),
          firstDate: firstDate,
          lastDate: lastDate,
          builder: (context, child) => _buildDatePickerTheme(context, child),
        );
        break;
    }

    if (selectedDate != null) {
      field.didChange(selectedDate);
      controller.setValue(config.key.value, selectedDate);
    }
  }

  /// Clamp date to valid range
  DateTime _clampDate(DateTime date, DateTime min, DateTime max) {
    if (date.isBefore(min)) return min;
    if (date.isAfter(max)) return max;
    return date;
  }

  /// Build themed date picker
  Widget _buildDatePickerTheme(BuildContext context, Widget? child) {
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: Theme.of(context).colorScheme.copyWith(
          primary: themeColor,
          onPrimary: UiThemeService().getContrastingTextColor(themeColor),
        ),
      ),
      child: child!,
    );
  }

  /// Build themed time picker
  Widget _buildTimePickerTheme(BuildContext context, Widget? child) {
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: Theme.of(context).colorScheme.copyWith(
          primary: themeColor,
          onPrimary: UiThemeService().getContrastingTextColor(themeColor),
        ),
        timePickerTheme: TimePickerThemeData(
          backgroundColor: Theme.of(context).cardColor,
          hourMinuteTextColor: themeColor,
          dayPeriodTextColor: themeColor,
          dialHandColor: themeColor,
          dialTextColor: Theme.of(context).textTheme.bodyLarge?.color,
        ),
      ),
      child: child!,
    );
  }
}
