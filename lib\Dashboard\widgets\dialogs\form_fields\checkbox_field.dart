import 'package:flutter/material.dart';
import '../form_config.dart';
import '../universal_form_controller.dart';
import '../config/ui_theme_service.dart';

/// Universal checkbox field widget for the form system
class UniversalCheckboxField extends StatelessWidget {
  final UniversalFormController controller;
  final FormFieldConfig config;
  final Color themeColor;

  const UniversalCheckboxField({
    Key? key,
    required this.controller,
    required this.config,
    required this.themeColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currentValue = controller.getValue(config.key.value) as bool? ?? false;
    final errorText = controller.validationErrors[config.key.value];

    return FormField<bool>(
      initialValue: currentValue,
      validator: (value) => errorText,
      builder: (FormFieldState<bool> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: config.enabled ? () => controller.setValue(config.key.value, !currentValue) : null,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16), // Optimized to match other fields
                decoration: BoxDecoration(
                  border: Border.all(
                    color: errorText != null
                        ? Colors.red
                        : themeColor.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    if (config.icon != null) ...[
                      Icon(
                        config.icon,
                        color: themeColor,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                    ],
                    Expanded(
                      child: Text(
                        config.label,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    Transform.scale(
                      scale: 1.1,
                      child: Checkbox(
                        value: currentValue,
                        onChanged: config.enabled ? (value) => controller.setValue(config.key.value, value) : null,
                        activeColor: themeColor,
                        checkColor: UiThemeService().getContrastingTextColor(themeColor),
                        side: BorderSide(
                          color: themeColor.withValues(alpha: 0.6),
                          width: 2,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (errorText != null) ...[
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Text(
                  errorText,
                  style: TextStyle(
                    color: Colors.red[700],
                    fontSize: 12,
                  ),
                ),
              ),
            ],
            if (config.hint != null) ...[
              const SizedBox(height: 4),
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Text(
                  config.hint!,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}
