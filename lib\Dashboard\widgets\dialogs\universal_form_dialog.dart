import 'package:flutter/material.dart';
import 'form_config.dart';
import 'universal_form_controller.dart';
import 'universal_form_builder.dart';
import 'config/ui_theme_service.dart';

/// Universal form dialog that provides a consistent, responsive shell
/// for all form dialogs in the application
///
/// Supports both internal controller creation (simple use cases) and external
/// controller injection (complex workflows like multi-step wizards)
class UniversalFormDialog extends StatefulWidget {
  final FormConfig config;
  final UniversalFormController? controller;
  final Map<String, dynamic>? initialValues;
  final Future<dynamic> Function(Map<String, dynamic> values)? onSave;
  final VoidCallback? onCancel;
  final String? section;
  final bool closeOnSave;

  const UniversalFormDialog({
    Key? key,
    required this.config,
    this.controller,
    this.initialValues,
    this.onSave,
    this.onCancel,
    this.section,
    this.closeOnSave = true,
  }) : super(key: key);

  /// Show the universal form dialog
  /// Supports both simple usage and complex workflows with external controllers
  static Future<T?> show<T>({
    required BuildContext context,
    required FormConfig config,
    UniversalFormController? controller,
    Map<String, dynamic>? initialValues,
    Future<dynamic> Function(Map<String, dynamic> values)? onSave,
    VoidCallback? onCancel,
    String? section,
    bool closeOnSave = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: false,
      builder: (context) => UniversalFormDialog(
        config: config,
        controller: controller,
        initialValues: initialValues,
        onSave: onSave,
        onCancel: onCancel,
        section: section,
        closeOnSave: closeOnSave,
      ),
    );
  }

  @override
  State<UniversalFormDialog> createState() => _UniversalFormDialogState();
}

class _UniversalFormDialogState extends State<UniversalFormDialog> {
  late UniversalFormController _controller;
  bool _ownsController = false; // Track if we created the controller
  late Color _headerColor;
  bool _isNavigating = false; // Prevent multiple navigation attempts

  @override
  void initState() {
    super.initState();

    // Use external controller or create internal one
    if (widget.controller != null) {
      _controller = widget.controller!;
      _ownsController = false;
    } else {
      _controller = UniversalFormController(
        fields: widget.config.fields,
        initialValues: widget.initialValues,
      );
      _ownsController = true;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Initialize theme-dependent properties here for proper DI pattern
    final uiTheme = UiThemeService.of(context);
    _headerColor = uiTheme.getColor(
      'dialog_header',
      section: widget.section ?? 'dialog',
    );
  }

  @override
  void dispose() {
    // Only dispose controller if we created it
    if (_ownsController) {
      _controller.dispose();
    }
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: _getDialogPadding(context),
      child: _buildDialogContent(context),
    );
  }

  /// Get responsive dialog padding
  EdgeInsets _getDialogPadding(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final isMobile = screenSize.width < 600;

    if (isMobile) {
      return const EdgeInsets.all(16);
    } else if (isTablet) {
      return const EdgeInsets.symmetric(horizontal: 40, vertical: 24);
    } else {
      return const EdgeInsets.symmetric(horizontal: 80, vertical: 40);
    }
  }

  /// Build main dialog content with responsive sizing
  Widget _buildDialogContent(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width >= 600 && screenSize.width < 1200;
    final isDesktop = screenSize.width >= 1200;
    final isMobile = screenSize.width < 600;

    // Responsive width calculation
    double dialogWidth;
    if (isMobile) {
      dialogWidth = screenSize.width - 32; // Full width minus padding
    } else if (isTablet) {
      dialogWidth = 600; // Fixed tablet width
    } else {
      dialogWidth = 800; // Larger desktop width
    }

    // Responsive height calculation
    double maxHeight;
    if (isMobile) {
      maxHeight = screenSize.height - 100; // Leave space for system UI
    } else {
      maxHeight = 700; // Standard height for larger screens
    }

    return Container(
      width: dialogWidth,
      constraints: BoxConstraints(
        maxHeight: maxHeight,
        maxWidth: dialogWidth,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).dialogBackgroundColor,
        borderRadius: BorderRadius.circular(12), // Consistent with pregnancy form
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(context),
          Flexible(
            child: _buildBody(context),
          ),
          _buildFooter(context),
        ],
      ),
    );
  }

  /// Get maximum width based on screen size
  double _getMaxWidth(double screenWidth) {
    if (screenWidth < 600) {
      return screenWidth - 32; // Mobile: full width minus padding
    } else if (screenWidth < 1200) {
      return 600; // Tablet: fixed width
    } else {
      return 800; // Desktop: larger fixed width
    }
  }

  /// Build responsive dialog header
  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    final uiTheme = UiThemeService.of(context);
    final mainColor = uiTheme.getMainColor();
    final screenSize = MediaQuery.of(context).size;
    final isMobile = screenSize.width < 600;

    // Compact padding to match pregnancy form
    final headerPadding = const EdgeInsets.symmetric(vertical: 16, horizontal: 16);

    final titleStyle = theme.textTheme.titleLarge?.copyWith(
      color: Colors.white,
      fontWeight: FontWeight.bold,
    );

    return Container(
      padding: headerPadding,
      decoration: BoxDecoration(
        color: mainColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getHeaderIcon(),
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          Flexible(
            child: Text(
              widget.config.title,
              style: titleStyle,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }



  /// Build modified indicator chip
  Widget _buildModifiedChip({required bool isCompact}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isCompact ? 4 : 6,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.edit,
            size: isCompact ? 8 : 10,
            color: Colors.orange[700],
          ),
          SizedBox(width: isCompact ? 1 : 2),
          Text(
            'Modified',
            style: TextStyle(
              color: Colors.orange[700],
              fontSize: isCompact ? 7 : 8,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Get appropriate header icon based on section
  IconData _getHeaderIcon() {
    switch (widget.section) {
      case 'health':
        return Icons.medical_services; // Health/medical icon
      case 'weight':
        return Icons.monitor_weight; // Weight scale icon
      case 'breeding':
        return Icons.favorite; // Heart icon for breeding
      case 'pregnancy':
        return Icons.pregnant_woman; // Pregnancy icon
      case 'milk':
        return Icons.local_drink; // Milk/drink icon
      case 'transaction':
        return Icons.attach_money; // Money icon
      case 'vaccination':
        return Icons.vaccines; // Vaccine icon
      case 'treatment':
        return Icons.healing; // Treatment icon
      default:
        return Icons.edit_note; // Default form icon
    }
  }

  /// Build dialog body with pregnancy form styling
  Widget _buildBody(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16), // Match pregnancy form padding
      child: Form(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 16), // Match pregnancy form padding
          child: UniversalFormBuilder(
            controller: _controller,
            fields: widget.config.fields,
            section: widget.section ?? 'form',
            scrollable: false, // We handle scrolling here
            padding: EdgeInsets.zero, // Remove default padding since we handle it
            spacing: 16, // Reduced spacing between fields
          ),
        ),
      ),
    );
  }

  /// Build responsive dialog footer
  Widget _buildFooter(BuildContext context) {
    final uiTheme = UiThemeService.of(context);
    final mainColor = uiTheme.getMainColor();
    final screenSize = MediaQuery.of(context).size;
    final isMobile = screenSize.width < 600;

    // Compact padding to match pregnancy form
    final footerPadding = const EdgeInsets.symmetric(vertical: 12, horizontal: 16);
    final buttonPadding = const EdgeInsets.symmetric(vertical: 12);

    final buttonSpacing = isMobile ? 12.0 : 16.0;

    return Container(
      padding: footerPadding,
      child: ListenableBuilder(
        listenable: _controller,
        builder: (context, _) {
          // Always use single row layout like pregnancy form
          return _buildSingleRowFooter(mainColor, buttonPadding, buttonSpacing);
        },
      ),
    );
  }



  /// Build desktop footer layout (side by side buttons)
  Widget _buildDesktopFooter(Color mainColor, EdgeInsets buttonPadding, double spacing) {
    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: _controller.isSaving ? null : _handleCancel,
            style: TextButton.styleFrom(
              padding: buttonPadding,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Cancel'),
          ),
        ),
        SizedBox(width: spacing),
        Expanded(
          child: ElevatedButton(
            onPressed: _controller.isSaving || !_controller.isValid ? null : _handleSave,
            style: ElevatedButton.styleFrom(
              backgroundColor: mainColor,
              foregroundColor: Colors.white,
              padding: buttonPadding,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _controller.isSaving
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Save'),
          ),
        ),
      ],
    );
  }

  /// Build single row footer layout (matches pregnancy form)
  Widget _buildSingleRowFooter(Color mainColor, EdgeInsets buttonPadding, double spacing) {
    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: _controller.isSaving ? null : _handleCancel,
            style: TextButton.styleFrom(
              padding: buttonPadding,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16), // Fixed spacing like pregnancy form
        Expanded(
          child: ElevatedButton(
            onPressed: _controller.isSaving || !_controller.isValid ? null : _handleSave,
            style: ElevatedButton.styleFrom(
              backgroundColor: mainColor,
              foregroundColor: Colors.white,
              padding: buttonPadding,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _controller.isSaving
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Save'),
          ),
        ),
      ],
    );
  }

  /// Handle save action
  Future<void> _handleSave() async {
    if (!_controller.validate()) {
      _showValidationErrors();
      return;
    }

    _controller.setSaving(true);

    try {
      if (widget.onSave != null) {
        final result = await widget.onSave!(_controller.formValues);
        // Check if the result indicates success (true, or any non-null object)
        final success = result == true || (result != null && result != false);
        if (success && mounted) {
          if (widget.closeOnSave && !_isNavigating) {
            _isNavigating = true;
            // Return the result from onSave if it's not a boolean, otherwise return form values
            final returnValue = (result is bool) ? _controller.formValues : result;
            // Simply close the dialog without complex navigation logic
            if (mounted) {
              try {
                Navigator.of(context).pop(returnValue);
              } catch (e) {
                // Silently handle navigation errors during hot reload
                debugPrint('Navigation error (likely hot reload): $e');
              }
            }
          } else {
            // Show success message when staying open
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Record saved successfully!'),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 2),
                ),
              );
              // Reset form for next entry
              _controller.reset();
            }
          }
        }
      } else {
        // No save callback, just return the values
        if (mounted && widget.closeOnSave && !_isNavigating) {
          _isNavigating = true;
          try {
            Navigator.of(context).pop(_controller.formValues);
          } catch (e) {
            // Silently handle navigation errors during hot reload
            debugPrint('Navigation error (likely hot reload): $e');
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving form: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        _controller.setSaving(false);
      }
    }
  }

  /// Handle cancel action with intelligent dirty state checking
  void _handleCancel() {
    // Check current dirty state without triggering rebuilds
    if (_controller.isDirty) {
      _showCancelConfirmation();
    } else {
      _performCancel();
    }
  }

  /// Show cancel confirmation dialog
  void _showCancelConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Discard Changes?'),
        content: const Text(
          'You have unsaved changes. Are you sure you want to discard them?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Keep Editing'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performCancel();
            },
            child: const Text('Discard'),
          ),
        ],
      ),
    );
  }

  /// Show validation errors in a clear dialog
  void _showValidationErrors() {
    final errors = _controller.validationErrors;
    if (errors.isEmpty) return;

    final errorMessages = <String>[];
    for (final field in widget.config.fields) {
      final fieldKey = field.key.value;
      if (errors.containsKey(fieldKey)) {
        errorMessages.add('• ${errors[fieldKey]}');
      }
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 24),
            const SizedBox(width: 8),
            const Expanded(
              child: Text('Required Fields Missing'),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Please fill in the following required fields:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 12),
            ...errorMessages.map((error) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                error,
                style: TextStyle(color: Colors.red[700]),
              ),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Perform cancel action
  void _performCancel() {
    if (widget.onCancel != null) {
      widget.onCancel!();
    }
    if (!_isNavigating && mounted) {
      _isNavigating = true;
      try {
        Navigator.of(context).pop();
      } catch (e) {
        // Silently handle navigation errors during hot reload
        debugPrint('Navigation error (likely hot reload): $e');
      }
    }
  }
}
