import 'package:flutter/material.dart';
import '../../widgets/dialogs/index.dart';
import '../models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../../utils/message_utils.dart';
import 'dart:math' as math;

/// Ultra-clean facade using centralized ModuleFormConfigs
class CattleFormDialog {
  static Future<void> show({
    required BuildContext context,
    CattleIsar? cattle,
    required List<AnimalTypeIsar> animalTypes,
    required List<CattleIsar> existingCattle,
    required Function(CattleIsar) onSave,
    required String businessId,
  }) async {
    const formConfigs = ModuleFormConfigs();

    await UniversalFormDialog.show<CattleIsar>(
      context: context,
      config: FormConfig.simple(
        title: cattle == null ? 'Add Cattle' : 'Edit Cattle',
        fields: formConfigs.forCattle(),
      ),
      initialValues: cattle != null ? {
        FormKeys.tagId.value: cattle.tagId ?? '',
        FormKeys.name.value: cattle.name ?? '',
        FormKeys.animalType.value: cattle.animalTypeId ?? '',
        FormKeys.gender.value: cattle.gender ?? '',
        FormKeys.breed.value: cattle.breedId ?? '',
        FormKeys.source.value: cattle.source ?? CattleSource.purchased.value,
        FormKeys.dateOfBirth.value: cattle.dateOfBirth,
        FormKeys.motherTagId.value: cattle.motherTagId ?? '',
        FormKeys.purchaseDate.value: cattle.purchaseDate,
        FormKeys.purchasePrice.value: cattle.purchasePrice ?? 0.0,
        FormKeys.weight.value: cattle.weight ?? 0.0,
        FormKeys.color.value: cattle.color ?? '',
        FormKeys.notes.value: cattle.notes ?? '',
        FormKeys.autoGenerateTagId.value: false,
      } : {
        FormKeys.autoGenerateTagId.value: true,
        FormKeys.source.value: CattleSource.purchased.value,
      },
      section: 'cattle',
      closeOnSave: true,
      onSave: (values) async {
        try {
          // Validate form data
          final validationError = _validateCattleForm(values, cattle, existingCattle, animalTypes);
          if (validationError != null) {
            CattleMessageUtils.showError(context, validationError);
            return false;
          }

          // Generate tag ID if auto-generate is enabled
          if (values[FormKeys.autoGenerateTagId.value] == true) {
            final currentTagId = values[FormKeys.tagId.value]?.toString().trim() ?? '';
            if (currentTagId.isEmpty) {
              values[FormKeys.tagId.value] = _generateTagId(values[FormKeys.animalType.value], existingCattle, animalTypes);
            }
          }

          final record = cattle ?? CattleIsar();
          record.businessId = businessId;
          record.tagId = values[FormKeys.tagId.value]?.toString().trim();
          record.name = values[FormKeys.name.value]?.toString().trim();
          record.animalTypeId = values[FormKeys.animalType.value]?.toString();
          record.breedId = values[FormKeys.breed.value]?.toString();
          record.gender = values[FormKeys.gender.value]?.toString();
          record.source = values[FormKeys.source.value]?.toString();
          record.dateOfBirth = values[FormKeys.dateOfBirth.value] as DateTime?;
          record.motherTagId = values[FormKeys.motherTagId.value]?.toString();
          record.purchaseDate = values[FormKeys.purchaseDate.value] as DateTime?;
          record.purchasePrice = values[FormKeys.purchasePrice.value] as double?;
          record.weight = values[FormKeys.weight.value] as double?;
          record.color = values[FormKeys.color.value]?.toString().trim();
          record.notes = values[FormKeys.notes.value]?.toString().trim();
          record.updatedAt = DateTime.now();

          if (cattle == null) {
            record.createdAt = DateTime.now();
            record.status = 'Active';
          }

          onSave(record);
          return record;
        } catch (e) {
          CattleMessageUtils.showError(context, 'Failed to save cattle record.');
          return false;
        }
      },
    );
  }

  /// Validate cattle form data with Tag ID logic
  static String? _validateCattleForm(
    Map<String, dynamic> values,
    CattleIsar? cattle,
    List<CattleIsar> existingCattle,
    List<AnimalTypeIsar> animalTypes,
  ) {
    // Basic required field validation
    if (_isEmpty(values[FormKeys.name.value])) {
      return 'Name is required';
    }
    if (_isEmpty(values[FormKeys.animalType.value])) {
      return 'Animal type is required';
    }
    if (_isEmpty(values[FormKeys.gender.value])) {
      return 'Gender is required';
    }
    if (_isEmpty(values[FormKeys.breed.value])) {
      return 'Breed is required';
    }
    if (_isEmpty(values[FormKeys.source.value])) {
      return 'Source is required';
    }

    // Tag ID validation
    final tagId = values[FormKeys.tagId.value]?.toString().trim() ?? '';
    final autoGenerate = values[FormKeys.autoGenerateTagId.value] == true;

    if (!autoGenerate && tagId.isEmpty) {
      return 'Tag ID is required';
    }

    if (!autoGenerate && tagId.isNotEmpty) {
      // Check format
      if (!_isValidTagIdFormat(tagId, values[FormKeys.animalType.value], animalTypes)) {
        return 'Tag ID must start with animal type first letter followed by numbers';
      }
      // Check uniqueness
      if (_isTagIdInUse(tagId, cattle, existingCattle)) {
        return 'This tag ID is already in use by another animal';
      }
    }

    // Source-specific validation
    final source = values[FormKeys.source.value];
    if (source == CattleSource.bornAtFarm.value) {
      if (values[FormKeys.dateOfBirth.value] == null) {
        return 'Date of birth is required for animals born at farm';
      }
      if (_isEmpty(values[FormKeys.motherTagId.value])) {
        return 'Mother tag ID is required for animals born at farm';
      }
    }

    if (source == CattleSource.purchased.value) {
      if (values[FormKeys.purchaseDate.value] == null) {
        return 'Purchase date is required for purchased animals';
      }
      final purchasePrice = values[FormKeys.purchasePrice.value];
      if (purchasePrice == null || purchasePrice <= 0) {
        return 'Purchase price is required for purchased animals';
      }
    }

    return null; // No validation errors
  }

  /// Generate tag ID based on animal type prefix
  static String _generateTagId(
    String? animalTypeId,
    List<CattleIsar> existingCattle,
    List<AnimalTypeIsar> animalTypes,
  ) {
    if (animalTypeId == null || animalTypeId.isEmpty) return '';

    final animalType = animalTypes.firstWhere(
      (type) => type.businessId == animalTypeId,
      orElse: () => AnimalTypeIsar(),
    );
    final prefix = animalType.name?.substring(0, 1).toUpperCase() ?? 'X';

    // Find max number among existing tags with this prefix
    int maxNumber = 0;
    final regExp = RegExp('^$prefix(\\d+)\$');

    for (final cattle in existingCattle) {
      final tagId = cattle.tagId;
      if (tagId != null && regExp.hasMatch(tagId)) {
        final match = regExp.firstMatch(tagId);
        if (match != null && match.groupCount >= 1) {
          final number = int.tryParse(match.group(1) ?? '0') ?? 0;
          maxNumber = math.max(maxNumber, number);
        }
      }
    }

    return '$prefix${maxNumber + 1}';
  }

  /// Validate tag ID format
  static bool _isValidTagIdFormat(
    String tagId,
    String? animalTypeId,
    List<AnimalTypeIsar> animalTypes,
  ) {
    if (animalTypeId == null || animalTypeId.isEmpty) return false;

    final animalType = animalTypes.firstWhere(
      (type) => type.businessId == animalTypeId,
      orElse: () => AnimalTypeIsar(),
    );
    final prefix = animalType.name?.substring(0, 1).toUpperCase() ?? 'X';

    final RegExp tagIdRegex = RegExp('^$prefix\\d+\$');
    return tagIdRegex.hasMatch(tagId);
  }

  /// Check if tag ID is already in use
  static bool _isTagIdInUse(
    String tagId,
    CattleIsar? currentCattle,
    List<CattleIsar> existingCattle,
  ) {
    return existingCattle.any((c) =>
        c.tagId == tagId &&
        (currentCattle == null || c.businessId != currentCattle.businessId));
  }

  /// Helper to check if value is empty
  static bool _isEmpty(dynamic value) {
    return value == null || (value is String && value.trim().isEmpty);
  }
}
