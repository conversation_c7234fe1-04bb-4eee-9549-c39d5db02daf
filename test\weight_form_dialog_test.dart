import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:cattle_manager/Dashboard/Weight/dialogs/weight_form_dialog.dart';
import 'package:cattle_manager/Dashboard/Cattle/models/cattle_isar.dart';

void main() {
  group('WeightFormDialog', () {
    late List<CattleIsar> testCattle;

    setUp(() {
      // Create test cattle data
      testCattle = [
        CattleIsar()
          ..businessId = 'cattle-1'
          ..name = '<PERSON><PERSON>'
          ..tagId = 'C001',
        CattleIsar()
          ..businessId = 'cattle-2'
          ..name = 'Daisy'
          ..tagId = 'C002',
      ];
    });

    testWidgets('WeightFormDialog.show creates and displays dialog', (WidgetTester tester) async {
      // Build a test app with the dialog
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => WeightFormDialog.show(
                  context: context,
                  cattle: testCattle,
                ),
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // Tap the button to show the dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify the dialog is displayed
      expect(find.text('Weight Record'), findsOneWidget);
      expect(find.text('Record a new weight measurement'), findsOneWidget);
      
      // Verify form fields are present
      expect(find.text('Select Cattle'), findsOneWidget);
      expect(find.text('Weight'), findsOneWidget);
      expect(find.text('Unit'), findsOneWidget);
      expect(find.text('Measurement Date'), findsOneWidget);
      expect(find.text('Method'), findsOneWidget);
      expect(find.text('Notes (Optional)'), findsOneWidget);
    });

    testWidgets('WeightFormDialog shows with pre-selected cattle', (WidgetTester tester) async {
      final preSelectedCattle = testCattle.first;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => WeightFormDialog.show(
                  context: context,
                  cattle: testCattle,
                  preSelectedCattle: preSelectedCattle,
                ),
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify the dialog is displayed
      expect(find.text('Weight Record'), findsOneWidget);
    });

    test('_mapRecordToFormValues correctly maps weight record', () {
      // This would require creating a mock WeightRecordIsar
      // For now, we'll just verify the method exists and can be called
      expect(WeightFormDialog, isNotNull);
    });

    test('_getDefaultValues returns correct default values', () {
      // This would require accessing private methods
      // For now, we'll just verify the class structure
      expect(WeightFormDialog, isNotNull);
    });
  });
}
