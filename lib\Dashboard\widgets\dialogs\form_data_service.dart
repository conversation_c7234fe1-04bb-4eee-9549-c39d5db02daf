import 'package:get_it/get_it.dart';
import 'dropdown_option.dart';

import '../../Cattle/services/cattle_handler.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';

import 'result.dart';
import 'form_enums.dart';

/// Interface for dropdown data loading services
/// Focused on loading dropdown options for forms
abstract class DropdownDataService {
  /// Load animal types for dropdown
  Future<ServiceResult<List<DropdownOption>>> loadAnimalTypes();

  /// Load breeds for a specific animal type
  Future<ServiceResult<List<DropdownOption>>> loadBreedsForAnimalType(String? animalTypeId);

  /// Load all breeds
  Future<ServiceResult<List<DropdownOption>>> loadAllBreeds();

  /// Load cattle for selection with type-safe parameters
  Future<ServiceResult<List<DropdownOption>>> loadCattle([CattleFilterParams? filters]);

  /// Load female cattle for mother selection
  Future<ServiceResult<List<DropdownOption>>> loadFemaleCattle();

  /// Load categories for transactions with type-safe category type
  Future<ServiceResult<List<DropdownOption>>> loadTransactionCategories(TransactionCategoryType categoryType);

  /// Load veterinarians
  Future<ServiceResult<List<DropdownOption>>> loadVeterinarians();
}

/// Interface for validation services
/// Focused on data validation and business rules
abstract class ValidationService {
  /// Check if tag ID is unique
  Future<ServiceResult<bool>> isTagIdUnique(String tagId, {String? excludeId});
}

/// Interface for ID generation services
/// Focused on generating unique identifiers
abstract class IdGenerationService {
  /// Generate next tag ID for animal type
  Future<ServiceResult<String>> generateTagId(String animalTypeId);
}

/// Combined interface for backward compatibility
/// This maintains the existing API while allowing for modular implementations
abstract class FormDataService implements DropdownDataService, ValidationService, IdGenerationService {}

/// Default implementation of FormDataService
/// This integrates with your existing services using dependency injection
class DefaultFormDataService implements FormDataService {
  final CattleHandler _cattleHandler;
  final FarmSetupHandler _farmSetupHandler;

  // In-memory cache for dropdown options
  final Map<String, List<DropdownOption>> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// Generate a consistent cache key with optional parameters
  String _cacheKey(String base, [Map<String, dynamic>? params]) {
    if (params == null || params.isEmpty) return base;
    final sortedKeys = params.keys.toList()..sort();
    final paramString = sortedKeys.map((k) => '$k=${params[k]}').join('&');
    return '$base?$paramString';
  }

  DefaultFormDataService({
    required CattleHandler cattleHandler,
    required FarmSetupHandler farmSetupHandler,
  }) : _cattleHandler = cattleHandler,
       _farmSetupHandler = farmSetupHandler;

  /// Helper method to check if cache is valid
  bool _isCacheValid(String key) {
    if (!_cache.containsKey(key) || !_cacheTimestamps.containsKey(key)) {
      return false;
    }
    final timestamp = _cacheTimestamps[key]!;
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  /// Helper method to cache data
  void _cacheData(String key, List<DropdownOption> data) {
    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
  }

  /// Clear all cached data
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  /// Clear specific cache entry
  void clearCacheEntry(String key) {
    _cache.remove(key);
    _cacheTimestamps.remove(key);
  }

  @override
  Future<ServiceResult<List<DropdownOption>>> loadAnimalTypes() async {
    const cacheKey = 'animal_types';

    // Check cache first
    if (_isCacheValid(cacheKey)) {
      return Result.success(_cache[cacheKey]!);
    }

    try {
      final animalTypes = await _farmSetupHandler.getAllAnimalTypes();
      final options = animalTypes.map((type) => DropdownOption(
        value: type.businessId ?? '',
        label: type.name ?? '',
      )).toList();

      // Cache the result
      _cacheData(cacheKey, options);
      return Result.success(options);
    } catch (e) {
      return Result.failure(ServiceError.databaseError(
        'Failed to load animal types',
        e,
      ));
    }
  }

  @override
  Future<ServiceResult<List<DropdownOption>>> loadBreedsForAnimalType(String? animalTypeId) async {
    if (animalTypeId == null) {
      return Result.failure(ServiceError.validationError('Animal type ID is required'));
    }

    final cacheKey = _cacheKey('breeds_for_animal_type', {'id': animalTypeId});

    // Check cache first
    if (_isCacheValid(cacheKey)) {
      return Result.success(_cache[cacheKey]!);
    }

    try {
      final breeds = await _farmSetupHandler.getBreedCategoriesForAnimalType(animalTypeId);
      final options = breeds.map((breed) => DropdownOption(
        value: breed.businessId ?? '',
        label: breed.name ?? '',
      )).toList();

      // Cache the result
      _cacheData(cacheKey, options);
      return Result.success(options);
    } catch (e) {
      return Result.failure(ServiceError.databaseError(
        'Failed to load breeds for animal type',
        e,
      ));
    }
  }

  @override
  Future<ServiceResult<List<DropdownOption>>> loadAllBreeds() async {
    const cacheKey = 'all_breeds';

    // Check cache first
    if (_isCacheValid(cacheKey)) {
      return Result.success(_cache[cacheKey]!);
    }

    try {
      final breeds = await _farmSetupHandler.getAllBreedCategories();
      final options = breeds.map((breed) => DropdownOption(
        value: breed.businessId ?? '',
        label: breed.name ?? '',
      )).toList();

      // Cache the result
      _cacheData(cacheKey, options);
      return Result.success(options);
    } catch (e) {
      return Result.failure(ServiceError.databaseError(
        'Failed to load all breeds',
        e,
      ));
    }
  }

  @override
  Future<ServiceResult<List<DropdownOption>>> loadCattle([CattleFilterParams? filters]) async {
    // Note: Cattle data is not cached as it changes frequently
    try {
      var cattle = await _cattleHandler.getAllCattle();

      if (filters != null) {
        if (filters.gender != null) {
          cattle = cattle.where((c) =>
            c.gender?.toLowerCase() == filters.gender!.value.toLowerCase()
          ).toList();
        }

        if (filters.animalTypeId != null) {
          cattle = cattle.where((c) => c.animalTypeId == filters.animalTypeId).toList();
        }

        if (filters.breedId != null) {
          cattle = cattle.where((c) => c.breedId == filters.breedId).toList();
        }

        if (filters.isActive != null) {
          // Filter by status - active cattle have status 'Active'
          cattle = cattle.where((c) =>
            filters.isActive! ? (c.status?.toLowerCase() == 'active') : (c.status?.toLowerCase() != 'active')
          ).toList();
        }
      }

      final options = cattle.map((c) => DropdownOption(
        value: c.businessId ?? '',
        label: '${c.tagId} - ${c.name ?? 'Unnamed'}',
      )).toList();

      return Result.success(options);
    } catch (e) {
      return Result.failure(ServiceError.databaseError(
        'Failed to load cattle',
        e,
      ));
    }
  }

  @override
  Future<ServiceResult<List<DropdownOption>>> loadTransactionCategories(TransactionCategoryType categoryType) async {
    final cacheKey = _cacheKey('transaction_categories', {'type': categoryType.value});

    // Check cache first
    if (_isCacheValid(cacheKey)) {
      return Result.success(_cache[cacheKey]!);
    }

    try {
      final categories = categoryType == TransactionCategoryType.income
          ? await _farmSetupHandler.getIncomeCategories()
          : await _farmSetupHandler.getExpenseCategories();
      final options = categories.map((cat) => DropdownOption(
        value: cat.categoryId,
        label: cat.name,
      )).toList();

      // Cache the result
      _cacheData(cacheKey, options);
      return Result.success(options);
    } catch (e) {
      return Result.failure(ServiceError.databaseError(
        'Failed to load transaction categories',
        e,
      ));
    }
  }

  @override
  Future<ServiceResult<List<DropdownOption>>> loadVeterinarians() async {
    const cacheKey = 'veterinarians';

    // Check cache first
    if (_isCacheValid(cacheKey)) {
      return Result.success(_cache[cacheKey]!);
    }

    try {
      // TODO: Integrate with your veterinarian data source when available
      // For now, return a basic list - this could be enhanced to load from a database table
      const options = [
        DropdownOption(value: 'dr_smith', label: 'Dr. Smith'),
        DropdownOption(value: 'dr_johnson', label: 'Dr. Johnson'),
        DropdownOption(value: 'dr_williams', label: 'Dr. Williams'),
      ];

      // Cache the result
      _cacheData(cacheKey, options);
      return Result.success(options);
    } catch (e) {
      return Result.failure(ServiceError.databaseError(
        'Failed to load veterinarians',
        e,
      ));
    }
  }

  @override
  Future<ServiceResult<bool>> isTagIdUnique(String tagId, {String? excludeId}) async {
    try {
      final existingCattle = await _cattleHandler.getCattleByTagId(tagId);
      if (existingCattle == null) return Result.success(true);
      if (excludeId != null && existingCattle.businessId == excludeId) return Result.success(true);
      return Result.success(false);
    } catch (e) {
      return Result.failure(ServiceError.databaseError(
        'Failed to check tag ID uniqueness',
        e,
      ));
    }
  }

  @override
  Future<ServiceResult<String>> generateTagId(String animalTypeId) async {
    try {
      // Get the animal type to determine prefix
      final animalTypes = await _farmSetupHandler.getAllAnimalTypes();
      final animalType = animalTypes.firstWhere(
        (type) => type.businessId == animalTypeId,
        orElse: () => throw Exception('Animal type not found'),
      );

      final prefix = animalType.name?.substring(0, 1).toUpperCase() ?? 'A';

      // Get all cattle and find the highest number for this prefix
      final allCattle = await _cattleHandler.getAllCattle();
      int maxNumber = 0;
      final regExp = RegExp('^$prefix(\\d+)\$');

      for (final cattle in allCattle) {
        final tagId = cattle.tagId;
        if (tagId != null && regExp.hasMatch(tagId)) {
          final match = regExp.firstMatch(tagId);
          if (match != null && match.groupCount >= 1) {
            final number = int.tryParse(match.group(1) ?? '0') ?? 0;
            if (number > maxNumber) {
              maxNumber = number;
            }
          }
        }
      }

      final tagId = '$prefix${(maxNumber + 1).toString().padLeft(3, '0')}';
      return Result.success(tagId);
    } catch (e) {
      // Try fallback approach
      try {
        final prefix = animalTypeId.substring(0, 1).toUpperCase();
        final timestamp = DateTime.now().millisecondsSinceEpoch % 1000;
        final fallbackTagId = '$prefix${timestamp.toString().padLeft(3, '0')}';
        return Result.success(fallbackTagId);
      } catch (fallbackError) {
        return Result.failure(ServiceError.databaseError(
          'Failed to generate tag ID',
          e,
        ));
      }
    }
  }

  @override
  Future<ServiceResult<List<DropdownOption>>> loadFemaleCattle() async {
    final filters = CattleFilterParams(gender: Gender.female);
    return await loadCattle(filters);
  }

  // Backward compatibility methods for existing code
  // These will be deprecated once all callers are updated to use Result types

  /// Legacy method for loading animal types (returns empty list on error)
  Future<List<DropdownOption>> loadAnimalTypesLegacy() async {
    final result = await loadAnimalTypes();
    return result.unwrapOr([]);
  }

  /// Legacy method for loading breeds for animal type (returns empty list on error)
  Future<List<DropdownOption>> loadBreedsForAnimalTypeLegacy(String? animalTypeId) async {
    final result = await loadBreedsForAnimalType(animalTypeId);
    return result.unwrapOr([]);
  }

  /// Legacy method for loading all breeds (returns empty list on error)
  Future<List<DropdownOption>> loadAllBreedsLegacy() async {
    final result = await loadAllBreeds();
    return result.unwrapOr([]);
  }

  /// Legacy method for loading cattle (returns empty list on error)
  Future<List<DropdownOption>> loadCattleLegacy({String? gender, String? animalType}) async {
    final filters = CattleFilterParams(
      gender: Gender.tryParse(gender),
      animalTypeId: animalType,
    );
    final result = await loadCattle(filters);
    return result.unwrapOr([]);
  }

  /// Legacy method for loading transaction categories (returns empty list on error)
  Future<List<DropdownOption>> loadTransactionCategoriesLegacy(String categoryType) async {
    final categoryTypeEnum = TransactionCategoryType.tryParse(categoryType);
    if (categoryTypeEnum == null) return [];

    final result = await loadTransactionCategories(categoryTypeEnum);
    return result.unwrapOr([]);
  }

  /// Legacy method for loading veterinarians (returns empty list on error)
  Future<List<DropdownOption>> loadVeterinariansLegacy() async {
    final result = await loadVeterinarians();
    return result.unwrapOr([]);
  }

  /// Legacy method for checking tag ID uniqueness (returns false on error)
  Future<bool> isTagIdUniqueLegacy(String tagId, {String? excludeId}) async {
    final result = await isTagIdUnique(tagId, excludeId: excludeId);
    return result.unwrapOr(false);
  }

  /// Legacy method for generating tag ID (returns fallback ID on error)
  Future<String> generateTagIdLegacy(String animalTypeId) async {
    final result = await generateTagId(animalTypeId);
    return result.unwrapOrElse((error) {
      // Fallback to timestamp-based ID
      final prefix = animalTypeId.substring(0, 1).toUpperCase();
      final timestamp = DateTime.now().millisecondsSinceEpoch % 1000;
      return '$prefix${timestamp.toString().padLeft(3, '0')}';
    });
  }
}

/// Service registration helper for dependency injection
/// This replaces the singleton factory pattern with proper DI
class FormDataServiceRegistration {
  /// Register FormDataService with GetIt dependency injection container
  /// This should be called during app initialization
  static void registerServices() {
    final getIt = GetIt.instance;

    // Only register if not already registered
    if (!getIt.isRegistered<FormDataService>()) {
      // Get the required dependencies from GetIt
      final cattleHandler = getIt<CattleHandler>();
      final farmSetupHandler = getIt<FarmSetupHandler>();

      // Create and register the service with its dependencies
      final formDataService = DefaultFormDataService(
        cattleHandler: cattleHandler,
        farmSetupHandler: farmSetupHandler,
      );

      getIt.registerSingleton<FormDataService>(formDataService);
    }
  }

  /// Register individual service interfaces for more granular DI
  static void registerModularServices() {
    final getIt = GetIt.instance;

    // Get the main service
    final formDataService = getIt<FormDataService>();

    // Register the individual interfaces pointing to the same instance
    if (!getIt.isRegistered<DropdownDataService>()) {
      getIt.registerSingleton<DropdownDataService>(formDataService);
    }
    if (!getIt.isRegistered<ValidationService>()) {
      getIt.registerSingleton<ValidationService>(formDataService);
    }
    if (!getIt.isRegistered<IdGenerationService>()) {
      getIt.registerSingleton<IdGenerationService>(formDataService);
    }
  }
}
