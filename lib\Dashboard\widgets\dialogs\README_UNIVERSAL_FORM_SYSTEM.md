# Universal Form System

A comprehensive, configuration-driven form system that replaces all hardcoded form dialogs with a single, powerful, and maintainable solution.

## Overview

The Universal Form System provides:
- **Configuration-driven forms** - Define forms through declarative configuration
- **Responsive layouts** - Multi-column layouts that adapt to screen size
- **Dynamic theming** - Unique colors for each field with banned color exclusion
- **Field dependencies** - Conditional field visibility and behavior
- **Comprehensive validation** - Built-in and custom validation rules
- **Transactional state** - Robust save/cancel with change tracking
- **Type safety** - Full TypeScript-like safety in Dart

## Architecture

```
lib/Dashboard/widgets/dialogs/
├── form_config.dart                 # Configuration models and enums
├── universal_form_controller.dart   # State management controller
├── universal_form_builder.dart      # Responsive form rendering engine
├── universal_form_dialog.dart       # Main dialog shell component
├── form_fields/                     # Individual field widgets
│   ├── text_field.dart
│   ├── dropdown_field.dart
│   ├── date_field.dart
│   ├── checkbox_field.dart
│   └── slider_field.dart
└── test_universal_form.dart         # Test implementation examples

lib/config/
├── ui_theme_service.dart            # Centralized color and theme management
└── module_form_configs.dart         # Form configurations for all modules
```

## Quick Start

### 1. Define Form Configuration

```dart
final weightFormConfig = FormConfig(
  title: 'Weight Record',
  subtitle: 'Record cattle weight measurement',
  fields: [
    FormFieldConfig(
      key: 'cattle',
      label: 'Select Cattle',
      type: FormFieldType.cattleSelector,
      required: true,
      validationRules: [ValidationRule.required],
    ),
    FormFieldConfig(
      key: 'weight',
      label: 'Weight',
      type: FormFieldType.number,
      columnSpan: 0.5, // Half width
      required: true,
      validationRules: [
        ValidationRule.required,
        ValidationRule.positiveNumber,
      ],
    ),
    FormFieldConfig(
      key: 'unit',
      label: 'Unit',
      type: FormFieldType.dropdown,
      columnSpan: 0.5, // Half width
      options: [
        DropdownOption(value: 'kg', label: 'Kilograms'),
        DropdownOption(value: 'lbs', label: 'Pounds'),
      ],
    ),
  ],
);
```

### 2. Show Form Dialog

```dart
final result = await UniversalFormDialog.show<Map<String, dynamic>>(
  context: context,
  config: weightFormConfig,
  initialValues: {'unit': 'kg'},
  section: 'weight_form',
  onSave: (values) async {
    // Your save logic here
    await saveWeightRecord(values);
    return true; // Return true for successful save
  },
);
```

## Field Types

### Supported Field Types

| Type | Description | Example Use |
|------|-------------|-------------|
| `text` | Single-line text input | Names, descriptions |
| `number` | Numeric input with decimal support | Weights, measurements |
| `dropdown` | Single selection dropdown | Categories, statuses |
| `date` | Date picker | Birth dates, event dates |
| `dateTime` | Date and time picker | Timestamps |
| `time` | Time picker | Scheduled times |
| `checkbox` | Boolean checkbox | Yes/no options |
| `slider` | Range slider | Confidence levels, ratings |
| `multiline` | Multi-line text input | Notes, descriptions |
| `currency` | Currency input | Prices, costs |
| `percentage` | Percentage input | Rates, percentages |
| `phone` | Phone number input | Contact numbers |
| `email` | Email input | Email addresses |
| `cattleSelector` | Cattle selection dropdown | Cattle references |

### Field Configuration Properties

```dart
FormFieldConfig(
  key: 'fieldKey',              // Unique identifier
  label: 'Field Label',         // Display label
  type: FormFieldType.text,     // Field type
  columnSpan: 1.0,              // Width (0.0-1.0)
  required: false,              // Required validation
  hint: 'Placeholder text',     // Hint text
  icon: Icons.example,          // Field icon
  initialValue: 'default',      // Default value
  validationRules: [...],       // Validation rules
  dependencies: [...],          // Field dependencies
  enabled: true,                // Enable/disable field
  visible: true,                // Show/hide field
)
```

## Responsive Layout

The system automatically creates responsive layouts:

- **Desktop (>1200px)**: Multi-column layout respecting `columnSpan`
- **Tablet (600-1200px)**: Adaptive layout with intelligent wrapping
- **Mobile (<600px)**: Single-column vertical layout

### Column Spanning

```dart
// Full width field
FormFieldConfig(columnSpan: 1.0, ...)

// Half width fields (side by side)
FormFieldConfig(columnSpan: 0.5, ...)
FormFieldConfig(columnSpan: 0.5, ...)

// One-third width fields
FormFieldConfig(columnSpan: 0.33, ...)
FormFieldConfig(columnSpan: 0.33, ...)
FormFieldConfig(columnSpan: 0.34, ...)
```

## Validation System

### Built-in Validation Rules

```dart
ValidationRule.required                    // Required field
ValidationRule.minLength(5)               // Minimum length
ValidationRule.maxLength(100)             // Maximum length
ValidationRule.minValue(0.0)              // Minimum numeric value
ValidationRule.maxValue(1000.0)           // Maximum numeric value
ValidationRule.email                      // Email format
ValidationRule.phone                      // Phone format
ValidationRule.positiveNumber             // Positive numbers only
ValidationRule.tagIdFormat                // Tag ID format (cattle-specific)
ValidationRule.uniqueTagId                // Unique tag ID validation
```

### Custom Validation

```dart
ValidationRule.custom(
  (value, formValues) {
    // Custom validation logic
    return value != null && value.toString().startsWith('C');
  },
  'Value must start with C',
)
```

## Field Dependencies

### Conditional Visibility

```dart
FormFieldConfig(
  key: 'pregnancyStage',
  label: 'Pregnancy Stage',
  type: FormFieldType.number,
  dependencies: [
    FieldDependency(
      parentFieldKey: 'isPregnant',
      parentValue: true, // Show only when pregnant is true
    ),
  ],
)
```

### Custom Conditions

```dart
FieldDependency(
  parentFieldKey: 'animalType',
  condition: (parentValue, currentValue) {
    return parentValue == 'cow' || parentValue == 'bull';
  },
)
```

## Dynamic Data Loading

### Async Dropdown Options

```dart
FormFieldConfig(
  key: 'breed',
  label: 'Breed',
  type: FormFieldType.dropdown,
  optionsLoader: () async {
    final breeds = await farmSetupService.getBreeds();
    return breeds.map((b) => DropdownOption(
      value: b.id,
      label: b.name,
    )).toList();
  },
)
```

## Color Management

The system automatically assigns unique colors to fields while excluding banned colors (orange, yellow, grey, amber, brown).

### Color Assignment

- Colors are assigned deterministically based on field keys
- No color repetition within the same form section
- Automatic contrast calculation for text readability
- Theme-consistent styling across all components

### Custom Color Sections

```dart
UniversalFormDialog.show(
  // ...
  section: 'weight_form', // Creates isolated color space
)
```

## Migration Guide

### Replacing Existing Forms

1. **Analyze existing form** - Identify all fields, validation, and behavior
2. **Create configuration** - Define FormFieldConfig for each field
3. **Add to ModuleFormConfigs** - Create static method returning field list
4. **Replace dialog calls** - Use UniversalFormDialog.show instead
5. **Test thoroughly** - Ensure all functionality is preserved
6. **Remove old files** - Delete original form dialog files

### Example Migration

**Before:**
```dart
// Old hardcoded form
showDialog(
  context: context,
  builder: (context) => WeightFormDialog(cattle: cattle),
);
```

**After:**
```dart
// New universal form with dependency injection
final formConfigs = ModuleFormConfigs();
final result = await UniversalFormDialog.show(
  context: context,
  config: FormConfig(
    title: 'Weight Record',
    fields: formConfigs.forWeight(),
  ),
  initialValues: {'cattle': cattle.id},
);
```

## Architecture Overview

### Service Layer Architecture

The Universal Form System now uses a sophisticated service layer with:

#### 1. Interface Segregation
- **DropdownDataService**: Handles dropdown option loading
- **ValidationService**: Manages data validation and business rules
- **IdGenerationService**: Generates unique identifiers
- **FormDataService**: Combined interface for backward compatibility

#### 2. Dependency Injection
- Uses GetIt for proper dependency injection
- No singleton factories or global state
- Services are registered during app initialization
- Dependencies are explicit and testable

#### 3. Result Type System
- All service methods return `ServiceResult<T>` instead of throwing exceptions
- Predictable error handling with detailed error types
- UI can distinguish between empty data and actual errors

#### 4. Intelligent Caching
- In-memory caching for rarely-changing data (animal types, breeds, etc.)
- 5-minute cache expiry with manual invalidation support
- Consistent cache key generation with parameter handling
- Cattle data is not cached as it changes frequently

#### 5. Type-Safe Form Field Utilities
- All logic moved directly into `FormFieldConfigExtensions` (eliminated utility class)
- Generic parsing and formatting methods: `parseValue<T>()`, `formatValueForDisplay<T>()`
- Runtime type checking with developer-friendly assertion messages
- Internationalized currency formatting using `intl` package
- Improved numeric input handling with better regex patterns
- Simplified slider division calculation based on decimal places
- Built-in validation methods: `isValidValue()`, `getValidationErrorMessage()`
- Convenience methods for validation rules: `isRequired`, `maxLength`, `isNumericField`

#### 6. Self-Contained Type-Safe Enums
- Enums contain all their own utility methods (no separate utility classes)
- Stable enum name-based parsing with `tryParse()` methods
- Built-in dropdown option generation: `Gender.dropdownOptions`
- Backward compatibility with deprecated legacy methods
- Robust parsing that's resilient to display name changes

### Example Usage

```dart
// Service layer with Result types
final result = await formDataService.loadAnimalTypes();
result.when(
  success: (options) => updateUI(options),
  failure: (error) => showError(error.userMessage),
);

// Type-safe enums with self-contained utilities
final genderOptions = Gender.dropdownOptions; // No utility class needed
final selectedGender = Gender.tryParse('male'); // Robust parsing
final cattleFilters = CattleFilterParams(
  gender: Gender.male,
  animalTypeId: 'cow_id',
).withBreed('holstein_id'); // Fluent API

// Form field utilities with runtime type checking
final config = FormFieldConfig<double>(
  key: FormKeys.weight,
  type: FormFieldType.number,
  decimalPlaces: 2,
);

// Type-safe parsing with developer-friendly error messages
final weight = config.parseValue<double>('75.5'); // Returns double?

// Robust validation
final isValid = config.isValidValue('75.5'); // true
final errorMessage = config.getValidationErrorMessage('invalid'); // "Weight must be a valid number"

// Formatted display with internationalization
final display = config.formatValueForDisplay(75.5); // Returns "$75.50" for currency

// Smart slider divisions
final divisions = config.sliderDivisions; // Calculated based on decimal places
```

## Testing

Use the test implementation to validate the system:

```dart
import 'test_universal_form.dart';

// Show test widget
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => TestUniversalForm.buildTestWidget(),
  ),
);
```

## Best Practices

1. **Use descriptive field keys** - Makes debugging easier
2. **Group related fields** - Use columnSpan for logical grouping
3. **Provide helpful hints** - Guide users with placeholder text
4. **Validate early** - Use built-in validation rules when possible
5. **Test on all screen sizes** - Ensure responsive behavior
6. **Use consistent sections** - Group related forms in same color section
7. **Handle async operations** - Use optionsLoader for dynamic data
8. **Implement proper error handling** - Handle save failures gracefully

## Performance Considerations

- Field widgets are optimized for minimal rebuilds
- Color assignments are cached per section
- Validation runs only on changed fields
- Async data loading includes proper loading states
- Form state is efficiently managed with ChangeNotifier

## Future Enhancements

- Additional field types (file upload, rich text, etc.)
- Form sections and collapsible groups
- Advanced validation with cross-field rules
- Form templates and presets
- Accessibility improvements
- Internationalization support
